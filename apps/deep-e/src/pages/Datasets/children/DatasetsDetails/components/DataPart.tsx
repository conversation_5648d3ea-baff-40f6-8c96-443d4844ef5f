import React, { useEffect, useState } from 'react';
import { Table, Modal, Card, Tag, Button } from 'antd';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { getDatasetDetail, getDatasetSingleOriginalImage } from '../../../api';
import { DatasetDetailItem } from '../../../api/model';
import { t } from '@/languages';
import { COCOAnnotation, COCOCategory, COCOImage } from './AnnotatedImage';

// Multimodal data structure types
interface MultimodalAnnotation {
  id: string;
  label: string;
  bboxes?: number[][];
  attributes?: Record<string, any>;
  segmentations?: number[][];
}

interface MultimodalAnnotationGroup {
  label: string;
  annotations: MultimodalAnnotation[];
}

interface MultimodalImageData {
  imageId: string;
  annotationGroups: MultimodalAnnotationGroup[];
}

interface MultimodalQAItem {
  question: string;
  answer: string;
  lang: string;
  type: string;
}

interface MultimodalDatasetDetailItem {
  qa: MultimodalQAItem[];
  images1?: string; // Base64 encoded image
  images1Name?: MultimodalImageData;
  images2?: string; // Base64 encoded image
  images2Name?: MultimodalImageData;
  [key: string]: any; // For additional image fields
}

const calculateContentLength = (value: any): number => {
  if (typeof value === 'string') return value.length;
  if (typeof value === 'number') return String(value).length;
  return JSON.stringify(value).length;
};

type DataPartProps = {
  datasetUuid: string;
  datasetType: number;
  changTotalData: (total: number) => void;
};

const DataPart: React.FC<DataPartProps> = ({
  datasetUuid,
  changTotalData,
  datasetType,
}) => {
  const [dataList, setDataList] = useState<DatasetDetailItem[]>([]);
  const [multimodalData, setMultimodalData] = useState<
    MultimodalDatasetDetailItem[]
  >([]);
  const [columnNames, setColumnNames] = useState<string[]>([]);

  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // Modal相关状态
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedImageData, setSelectedImageData] = useState<{
    imageUrl: string;
    annotations: COCOAnnotation[];
    categories: COCOCategory[];
    imageInfo: COCOImage;
  } | null>(null);

  // 图片翻动相关状态
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [currentRowData, setCurrentRowData] =
    useState<MultimodalDatasetDetailItem | null>(null);
  const [imageList, setImageList] = useState<
    Array<{
      columnName: string;
      imageId: string;
      imageUrl?: string; // 缓存的base64图片URL
      annotationData: MultimodalImageData;
    }>
  >([]);

  // 图片缓存
  const [imageCache, setImageCache] = useState<Record<string, string>>({});

  // 缩略图组件（用于列表显示）
  const ThumbnailImage: React.FC<{
    imageId: string;
    annotationData: MultimodalImageData;
    record: MultimodalDatasetDetailItem;
    columnName: string;
  }> = ({ imageId, annotationData, record, columnName }) => {
    // 生成缩略图占位符
    const thumbnailUrl = `data:image/svg+xml;base64,${btoa(`
      <svg width="100" height="75" viewBox="0 0 100 75" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="100" height="75" fill="#f0f0f0"/>
        <rect x="35" y="25" width="30" height="25" fill="#d9d9d9"/>
        <circle cx="42" cy="32" r="3" fill="#bfbfbf"/>
        <polygon points="35,45 42,38 48,42 58,35 65,42 65,50 35,50" fill="#bfbfbf"/>
        <text x="50" y="65" text-anchor="middle" fill="#999" font-size="8">${imageId.substring(0, 8)}...</text>
      </svg>
    `)}`;

    return (
      <div
        style={{
          width: 100,
          height: 75,
          borderRadius: 4,
          cursor: 'pointer',
          border: '1px solid #d9d9d9',
          overflow: 'hidden',
        }}
        onClick={(event) =>
          handleMultimodalImageClick('', annotationData, record, event)
        }>
        <img
          src={thumbnailUrl}
          alt={columnName}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
          }}
        />
      </div>
    );
  };

  // 可缩放拖拽的图片组件
  const ZoomableImage: React.FC<{
    imageUrl: string;
    alt: string;
  }> = ({ imageUrl, alt }) => {
    const [scale, setScale] = useState(1);
    const [position, setPosition] = useState({ x: 0, y: 0 });
    const [isDragging, setIsDragging] = useState(false);
    const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
    const [imageLoaded, setImageLoaded] = useState(false);

    // 重置图片状态
    const resetImage = () => {
      setScale(1);
      setPosition({ x: 0, y: 0 });
    };

    // 处理滚轮缩放
    const handleWheel = (event: React.WheelEvent) => {
      event.preventDefault();
      const delta = event.deltaY > 0 ? -0.1 : 0.1;
      const newScale = Math.max(0.1, Math.min(5, scale + delta));
      setScale(newScale);
    };

    // 处理鼠标按下
    const handleMouseDown = (event: React.MouseEvent) => {
      if (event.button === 2) {
        // 右键
        event.preventDefault();
        setIsDragging(true);
        setDragStart({
          x: event.clientX - position.x,
          y: event.clientY - position.y,
        });
      }
    };

    // 处理鼠标移动
    const handleMouseMove = (event: React.MouseEvent) => {
      if (isDragging) {
        setPosition({
          x: event.clientX - dragStart.x,
          y: event.clientY - dragStart.y,
        });
      }
    };

    // 处理鼠标抬起
    const handleMouseUp = () => {
      setIsDragging(false);
    };

    // 阻止右键菜单
    const handleContextMenu = (event: React.MouseEvent) => {
      event.preventDefault();
    };

    // 当图片URL改变时重置状态
    useEffect(() => {
      resetImage();
      setImageLoaded(false);
    }, [imageUrl]);

    return (
      <div
        style={{
          width: '100%',
          height: '600px',
          overflow: 'hidden',
          position: 'relative',
          border: '1px solid #d9d9d9',
          borderRadius: '6px',
          cursor: isDragging ? 'grabbing' : 'grab',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#fafafa',
        }}
        onWheel={handleWheel}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onContextMenu={handleContextMenu}>
        {!imageLoaded && (
          <div style={{ color: '#999', fontSize: '16px' }}>
            {t('加载中')}...
          </div>
        )}

        <img
          src={imageUrl}
          alt={alt}
          style={{
            transform: `translate(${position.x}px, ${position.y}px) scale(${scale})`,
            transition: isDragging ? 'none' : 'transform 0.1s ease-out',
            maxWidth: 'none',
            maxHeight: 'none',
            userSelect: 'none',
            pointerEvents: 'none',
            display: imageLoaded ? 'block' : 'none',
          }}
          onLoad={() => setImageLoaded(true)}
          onError={() => setImageLoaded(true)}
        />

        {/* 缩放控制提示 */}
        <div
          style={{
            position: 'absolute',
            bottom: '10px',
            right: '10px',
            background: 'rgba(0, 0, 0, 0.7)',
            color: 'white',
            padding: '4px 8px',
            borderRadius: '4px',
            fontSize: '12px',
            pointerEvents: 'none',
          }}>
          {Math.round(scale * 100)}% | 滚轮缩放 | 右键拖拽
        </div>
      </div>
    );
  };

  // 获取图片URL（从缓存或API）
  const getImageUrl = async (imageId: string): Promise<string> => {
    // 先检查缓存
    if (imageCache[imageId]) {
      return imageCache[imageId];
    }

    try {
      const response = await getDatasetSingleOriginalImage({
        imageId,
        datasetUuid,
      });

      if (response.code === 0 && response.data.result) {
        const imageUrl = `data:image/jpeg;base64,${response.data.result}`;
        // 缓存图片
        setImageCache((prev) => ({
          ...prev,
          [imageId]: imageUrl,
        }));
        return imageUrl;
      }
    } catch (error) {
      console.error('获取图片失败:', error);
    }

    // 返回默认占位图片
    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9Ijc1IiB2aWV3Qm94PSIwIDAgMTAwIDc1IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iNzUiIGZpbGw9IiNmMGYwZjAiLz48dGV4dCB4PSI1MCIgeT0iNDAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZpbGw9IiM5OTkiPuWKoOi9veWkseaViTwvdGV4dD48L3N2Zz4=';
  };

  // 从多模态标注数据生成COCO格式标注数据
  const generateCOCODataFromMultimodalAnnotation = (
    imageUrl: string,
    annotationData: MultimodalImageData,
  ) => {
    const annotationGroups = annotationData.annotationGroups || [];

    // 从标注组中提取所有唯一的标签作为类别
    const uniqueLabels = new Set<string>();
    annotationGroups.forEach((group) => {
      group.annotations.forEach((ann) => {
        uniqueLabels.add(ann.label);
      });
    });

    const mockCategories: COCOCategory[] = Array.from(uniqueLabels).map(
      (label, index) => ({
        id: index + 1,
        name: label,
        supercategory:
          annotationGroups.find((group) =>
            group.annotations.some((ann) => ann.label === label),
          )?.label || 'object',
      }),
    );

    const mockImageInfo: COCOImage = {
      id: 1,
      file_name: annotationData.imageId || 'multimodal_image.jpg',
      width: 640,
      height: 480,
      url: imageUrl,
    };

    // 将多模态格式的标注转换为COCO格式
    const mockAnnotations: COCOAnnotation[] = [];
    let annotationId = 1;

    annotationGroups.forEach((group) => {
      group.annotations.forEach((annotation) => {
        const categoryId =
          mockCategories.findIndex((cat) => cat.name === annotation.label) + 1;

        if (annotation.bboxes) {
          annotation.bboxes.forEach((bbox) => {
            // 假设bbox格式为 [x, y, width, height]
            const [x, y, width, height] = bbox;

            mockAnnotations.push({
              id: annotationId++,
              image_id: 1,
              category_id: categoryId,
              bbox: [x, y, width, height],
              area: width * height,
              iscrowd: 0,
              segmentation: annotation.segmentations || [
                [x, y, x + width, y, x + width, y + height, x, y + height],
              ],
            });
          });
        }
      });
    });

    return {
      imageUrl,
      annotations: mockAnnotations,
      categories: mockCategories,
      imageInfo: mockImageInfo,
    };
  };

  // 处理多模态图片点击事件
  const handleMultimodalImageClick = async (
    clickedImageUrl: string,
    clickedAnnotationData: MultimodalImageData,
    record: MultimodalDatasetDetailItem,
    event: React.MouseEvent,
  ) => {
    event.stopPropagation(); // 阻止事件冒泡，避免触发行点击

    // 收集当前行的所有图片信息
    const images: Array<{
      columnName: string;
      imageId: string;
      imageUrl?: string;
      annotationData: MultimodalImageData;
    }> = [];

    // 遍历columnNames，找到所有图片列
    columnNames.forEach((columnName) => {
      if (columnName.startsWith('images') && !columnName.endsWith('Name')) {
        const annotationKey = `${columnName}Name`;
        const annotationData = record[annotationKey] as MultimodalImageData;

        if (annotationData && annotationData.imageId) {
          images.push({
            columnName,
            imageId: annotationData.imageId,
            imageUrl: clickedImageUrl, // 只有点击的图片有URL
            annotationData: annotationData,
          });
        }
      }
    });

    // 找到当前点击图片的索引
    const clickedIndex = images.findIndex(
      (img) => img.imageUrl === clickedImageUrl,
    );

    setImageList(images);
    setCurrentRowData(record);
    setCurrentImageIndex(clickedIndex >= 0 ? clickedIndex : 0);

    // 设置当前图片的标注数据
    const currentImage = images[clickedIndex >= 0 ? clickedIndex : 0];
    if (currentImage) {
      const imageUrl = await getImageUrl(currentImage.imageId);
      const cocoData = generateCOCODataFromMultimodalAnnotation(
        imageUrl,
        currentImage.annotationData,
      );
      setSelectedImageData(cocoData);
    } else {
      // 如果没有找到图片，使用点击的图片数据
      const cocoData = generateCOCODataFromMultimodalAnnotation(
        clickedImageUrl,
        clickedAnnotationData || {
          imageId: 'unknown_image',
          annotationGroups: [],
        },
      );
      setSelectedImageData(cocoData);
    }

    setModalVisible(true);
  };

  // 切换到上一张图片
  const handlePreviousImage = async () => {
    if (imageList.length <= 1) return;

    const newIndex =
      currentImageIndex > 0 ? currentImageIndex - 1 : imageList.length - 1;
    setCurrentImageIndex(newIndex);

    const currentImage = imageList[newIndex];
    if (currentImage) {
      const imageUrl = await getImageUrl(currentImage.imageId);
      const cocoData = generateCOCODataFromMultimodalAnnotation(
        imageUrl,
        currentImage.annotationData,
      );
      setSelectedImageData(cocoData);
    }
  };

  // 切换到下一张图片
  const handleNextImage = async () => {
    if (imageList.length <= 1) return;

    const newIndex =
      currentImageIndex < imageList.length - 1 ? currentImageIndex + 1 : 0;
    setCurrentImageIndex(newIndex);

    const currentImage = imageList[newIndex];
    if (currentImage) {
      const imageUrl = await getImageUrl(currentImage.imageId);
      const cocoData = generateCOCODataFromMultimodalAnnotation(
        imageUrl,
        currentImage.annotationData,
      );
      setSelectedImageData(cocoData);
    }
  };

  const fetchDetail = async (page = 1, pageSize = 10) => {
    if (!datasetUuid) return;

    setLoading(true);
    try {
      const res = await getDatasetDetail({
        datasetUuid,
        page,
        pageSize,
        datasetType,
      });
      if (res.code === 0) {
        if (datasetType === 2) {
          // Multimodal dataset
          const multimodalResponse = res.data as any;
          if (multimodalResponse.list) {
            const columnNameList = multimodalResponse.list.columnNameList || [];
            const datasetDetailList =
              multimodalResponse.list.datasetDetailList || [];

            setColumnNames(columnNameList);
            setMultimodalData(datasetDetailList);
            setPagination({
              current: multimodalResponse.page || 1,
              pageSize: multimodalResponse.pageSize || 10,
              total: multimodalResponse.total || 0,
            });
            changTotalData(multimodalResponse.total || 0);
          }
        } else {
          // Single-modal dataset (datasetType === 1)
          const list = Array.isArray(res.data) ? res.data : res.data.list;
          setDataList(list || []);

          if (res.data.page) {
            setPagination({
              current: res.data.page,
              pageSize: res.data.pageSize || 10,
              total: res.data.total || 0,
            });
            changTotalData(res.data.total || 0);
          }
        }
      }
    } catch (error) {
      console.error(t('获取数据集详情失败：'), error);
    } finally {
      setLoading(false);
    }
  };

  const handleTableChange = (newPagination: any) => {
    fetchDetail(newPagination.current, newPagination.pageSize);
  };
  const handleRowClick = (
    record: DatasetDetailItem | MultimodalDatasetDetailItem,
    index: number,
  ) => {
    const recordKey = 'id' in record ? String(record.id) : String(index);
    setExpandedRowKeys((prev) =>
      prev.includes(recordKey)
        ? prev.filter((key) => key !== recordKey)
        : [...prev, recordKey],
    );
  };

  // Generate multimodal table columns
  const generateMultimodalColumns =
    (): ColumnsType<MultimodalDatasetDetailItem> => {
      const baseColumns = [
        {
          title: t('序号'),
          dataIndex: 'index',
          key: 'index',
          width: 80,
          align: 'center' as const,
          render: (_: unknown, __: unknown, index: number) => index + 1,
        },
      ];

      if (!columnNames.length) return baseColumns;

      // 严格按照 columnNameList 的顺序生成列
      const dynamicColumns = columnNames.map((columnName) => ({
        title: getColumnTitle(columnName),
        dataIndex: columnName,
        key: columnName,
        width: getColumnWidth(columnName),
        render: (
          value: any,
          record: MultimodalDatasetDetailItem,
          index: number,
        ) => {
          const isExpanded = expandedRowKeys.includes(String(index));
          return renderMultimodalCell(
            columnName,
            value,
            record,
            isExpanded,
            index,
          );
        },
      }));

      return [...baseColumns, ...dynamicColumns];
    };

  // Get column title for multimodal data
  const getColumnTitle = (columnName: string): string => {
    const titleMap: Record<string, string> = {
      qa: t('问答对'),
      images1: t('图片1'),
      images1Name: t('图片1标注'),
      images2: t('图片2'),
      images2Name: t('图片2标注'),
    };
    return titleMap[columnName] || columnName;
  };

  // Get column width for multimodal data
  const getColumnWidth = (columnName: string): number => {
    const widthMap: Record<string, number> = {
      qa: 300,
      images1: 150,
      images1Name: 200,
      images2: 150,
      images2Name: 200,
    };
    return widthMap[columnName] || 150;
  };

  // Render multimodal cell content
  const renderMultimodalCell = (
    columnName: string,
    value: any,
    record: MultimodalDatasetDetailItem,
    isExpanded: boolean,
    _index: number,
  ) => {
    if (columnName === 'qa') {
      if (!value || !Array.isArray(value)) return '-';
      return (
        <div
          className={`${isExpanded ? 'max-h-none' : 'max-h-[200px]'} overflow-auto transition-all duration-200`}
          style={{
            fontFamily: 'monospace',
            fontSize: '12px',
            backgroundColor: '#f5f5f5',
            padding: '8px',
            borderRadius: '4px',
            whiteSpace: 'pre-wrap',
          }}>
          {JSON.stringify(value, null, 2)}
        </div>
      );
    }

    if (columnName.startsWith('images') && columnName.endsWith('Name')) {
      // Handle annotation data (images1Name, images2Name, etc.) - 显示完整JSON
      if (!value || typeof value !== 'object') return '-';
      return (
        <div
          className={`${isExpanded ? 'max-h-none' : 'max-h-[200px]'} overflow-auto transition-all duration-200`}
          style={{
            fontFamily: 'monospace',
            fontSize: '12px',
            backgroundColor: '#f5f5f5',
            padding: '8px',
            borderRadius: '4px',
            whiteSpace: 'pre-wrap',
          }}>
          {JSON.stringify(value, null, 2)}
        </div>
      );
    }

    if (columnName.startsWith('images') && !columnName.endsWith('Name')) {
      // Handle image data using imageId from annotation
      const annotationKey = `${columnName}Name`;
      const annotationData = record[annotationKey] as MultimodalImageData;

      if (!annotationData || !annotationData.imageId) {
        return '-';
      }

      return (
        <ThumbnailImage
          imageId={annotationData.imageId}
          annotationData={annotationData}
          record={record}
          columnName={columnName}
        />
      );
    }

    // Default rendering for other fields
    if (typeof value === 'string' || typeof value === 'number') {
      return (
        <div
          className={`${isExpanded ? 'max-h-none' : 'max-h-[100px]'} overflow-hidden transition-all duration-200`}>
          {value}
        </div>
      );
    }

    return (
      <div
        className={`${isExpanded ? 'max-h-none' : 'max-h-[100px]'} overflow-hidden transition-all duration-200`}>
        {JSON.stringify(value)}
      </div>
    );
  };

  // Generate single-modal table columns (existing logic)
  const generateSingleModalColumns = (): ColumnsType<DatasetDetailItem> => {
    if (!dataList.length) return [];

    // 计算每列内容的最大长度
    const columnLengths: Record<string, number> = {};
    const baseColumns = [
      {
        title: t('序号'),
        dataIndex: 'index',
        key: 'index',
        width: 80,
        align: 'center' as const,
        render: (_: unknown, __: unknown, index: number) => index + 1,
      },
    ];

    if (!dataList[0]?.datasetDetail) return baseColumns;

    // 计算每列内容的最大长度
    Object.keys(dataList[0].datasetDetail).forEach((key) => {
      columnLengths[key] = dataList.reduce((maxLength, item) => {
        const contentLength = calculateContentLength(item.datasetDetail[key]);
        return Math.max(maxLength, contentLength);
      }, key.length);
    });

    // 计算总长度和比例
    const totalLength = Object.values(columnLengths).reduce(
      (sum, len) => sum + len,
      0,
    );
    const tableWidth = window.innerWidth - 200; // 预留边距和滚动条空间

    // 定义友好的列标题映射
    const columnTitleMap: Record<string, string> = {
      messages: t('对话消息'),
    };

    const dynamicColumns = Object.keys(dataList[0].datasetDetail).map(
      (key) => ({
        title: columnTitleMap[key] || key,
        dataIndex: ['datasetDetail', key],
        key,
        width: Math.max(
          100,
          Math.floor((columnLengths[key] / totalLength) * tableWidth),
        ),
        render: (value: any, record: DatasetDetailItem) => {
          const isExpanded = expandedRowKeys.includes(record.id.toString());

          // 特殊处理新数据结构的字段
          if (key === 'qa') {
            // qa字段直接显示为格式化的JSON字符串
            return (
              <div
                className={`${isExpanded ? 'max-h-none' : 'max-h-[200px]'} overflow-auto transition-all duration-200`}
                style={{
                  fontFamily: 'monospace',
                  fontSize: '12px',
                  backgroundColor: '#f5f5f5',
                  padding: '8px',
                  borderRadius: '4px',
                  whiteSpace: 'pre-wrap',
                }}>
                {value}
              </div>
            );
          }

          if (typeof value === 'string' || typeof value === 'number') {
            return (
              <div
                className={`${isExpanded ? 'max-h-none' : 'max-h-[100px]'} overflow-hidden transition-all duration-200`}>
                {value}
              </div>
            );
          }
          return (
            <div
              className={`${isExpanded ? 'max-h-none' : 'max-h-[100px]'} overflow-hidden transition-all duration-200`}>
              {JSON.stringify(value)}
            </div>
          );
        },
      }),
    );

    return [...baseColumns, ...dynamicColumns];
  };

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!modalVisible || imageList.length <= 1) return;

      if (event.key === 'ArrowLeft') {
        event.preventDefault();
        handlePreviousImage();
      } else if (event.key === 'ArrowRight') {
        event.preventDefault();
        handleNextImage();
      }
    };

    if (modalVisible) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [modalVisible, currentImageIndex, imageList.length]);

  useEffect(() => {
    fetchDetail();
  }, []);

  return (
    <>
      {datasetType === 2 ? (
        <Table<MultimodalDatasetDetailItem>
          bordered
          rowKey={(_, index) => String(index)}
          columns={generateMultimodalColumns()}
          dataSource={multimodalData}
          loading={loading}
          pagination={pagination}
          onChange={handleTableChange}
          onRow={(record, index) => ({
            onClick: () => handleRowClick(record, index || 0),
            style: { cursor: 'pointer' },
          })}
          scroll={{ x: 'max-content' }}
        />
      ) : (
        <Table<DatasetDetailItem>
          bordered
          rowKey='id'
          columns={generateSingleModalColumns()}
          dataSource={dataList}
          loading={loading}
          pagination={pagination}
          onChange={handleTableChange}
          onRow={(record, index) => ({
            onClick: () => handleRowClick(record, index || 0),
            style: { cursor: 'pointer' },
          })}
          scroll={{ x: 'max-content' }}
        />
      )}

      {/* COCO标注信息Modal */}
      <Modal
        title={
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
            <span>{t('图片标注信息 (COCO格式)')}</span>
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <Button
                type='text'
                icon={<LeftOutlined />}
                onClick={handlePreviousImage}
                disabled={imageList.length <= 1}
              />
              <span style={{ fontSize: '14px', color: '#666' }}>
                {imageList.length > 0
                  ? `${currentImageIndex + 1} / ${imageList.length}`
                  : '1 / 1'}
              </span>
              <Button
                type='text'
                icon={<RightOutlined />}
                onClick={handleNextImage}
                disabled={imageList.length <= 1}
              />
            </div>
          </div>
        }
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={1200}
        style={{ top: 20 }}>
        {selectedImageData && (
          <div
            style={{
              height: '80vh',
              display: 'flex',
              flexDirection: 'column',
            }}>
            {/* 图片信息栏 */}
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: '8px 16px',
                background: '#fafafa',
                borderRadius: '6px',
                marginBottom: '16px',
              }}>
              <div style={{ display: 'flex', gap: 16 }}>
                <span>
                  <strong>{t('文件名')}:</strong>{' '}
                  {selectedImageData.imageInfo.file_name}
                </span>
                <span>
                  <strong>{t('尺寸')}:</strong>{' '}
                  {selectedImageData.imageInfo.width} ×{' '}
                  {selectedImageData.imageInfo.height}
                </span>
                <span>
                  <strong>ID:</strong> {selectedImageData.imageInfo.id}
                </span>
              </div>
              {imageList.length > 0 && imageList[currentImageIndex] && (
                <Tag color='blue'>
                  {getColumnTitle(imageList[currentImageIndex].columnName)}
                </Tag>
              )}
            </div>

            {/* 主要内容区域 */}
            <div style={{ display: 'flex', gap: 16, flex: 1, minHeight: 0 }}>
              {/* 图片显示区域 */}
              <div style={{ flex: 2 }}>
                <ZoomableImage
                  imageUrl={selectedImageData.imageUrl}
                  alt={selectedImageData.imageInfo.file_name}
                />
              </div>

              {/* 标注信息区域 */}
              <div
                style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
                <Card
                  title={t('标注信息')}
                  size='small'
                  style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
                  <div style={{ flex: 1, overflow: 'auto' }}>
                    <div style={{ marginBottom: 16 }}>
                      <h4>{t('类别信息')}</h4>
                      {selectedImageData.categories.map((category) => (
                        <Tag
                          key={category.id}
                          color='green'
                          style={{ marginBottom: 4 }}>
                          {category.name}
                        </Tag>
                      ))}
                    </div>

                    <div>
                      <h4>{t('标注详情')}</h4>
                      <div style={{ maxHeight: '400px', overflow: 'auto' }}>
                        {selectedImageData.annotations.map((annotation) => (
                          <Card
                            key={annotation.id}
                            size='small'
                            style={{ marginBottom: 8 }}>
                            <p>
                              <strong>ID:</strong> {annotation.id}
                            </p>
                            <p>
                              <strong>{t('类别')}:</strong>{' '}
                              {selectedImageData.categories.find(
                                (c) => c.id === annotation.category_id,
                              )?.name || 'Unknown'}
                            </p>
                            <p>
                              <strong>Bbox:</strong> [
                              {annotation.bbox.join(', ')}]
                            </p>
                            <p>
                              <strong>{t('面积')}:</strong> {annotation.area}
                            </p>
                          </Card>
                        ))}
                      </div>
                    </div>
                  </div>
                </Card>
              </div>
            </div>

            {/* 类别信息 */}
            <Card
              title={t('标注类别')}
              size='small'
              style={{ marginBottom: 16 }}>
              <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>
                {selectedImageData.categories.map((category) => (
                  <Tag key={category.id} color='blue'>
                    {category.name} (ID: {category.id})
                  </Tag>
                ))}
              </div>
            </Card>

            {/* 标注信息 */}
            <Card title={t('标注详情')} size='small'>
              {selectedImageData.annotations.map((annotation, index) => {
                const category = selectedImageData.categories.find(
                  (cat) => cat.id === annotation.category_id,
                );
                return (
                  <div
                    key={annotation.id}
                    style={{
                      marginBottom: 16,
                      padding: 12,
                      border: '1px solid #f0f0f0',
                      borderRadius: 6,
                    }}>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        marginBottom: 8,
                      }}>
                      <Tag color='green'>
                        {t('标注')} {index + 1}
                      </Tag>
                      <Tag color='orange'>
                        {category?.name || t('未知类别')}
                      </Tag>
                    </div>
                    <div
                      style={{
                        display: 'grid',
                        gridTemplateColumns: '1fr 1fr',
                        gap: 8,
                        fontSize: '12px',
                      }}>
                      <div>
                        <strong>{t('标注ID')}:</strong> {annotation.id}
                      </div>
                      <div>
                        <strong>{t('类别ID')}:</strong> {annotation.category_id}
                      </div>
                      <div>
                        <strong>{t('边界框')}:</strong> [
                        {annotation.bbox.join(', ')}]
                      </div>
                      <div>
                        <strong>{t('面积')}:</strong> {annotation.area}
                      </div>
                      <div>
                        <strong>{t('是否群体')}:</strong>{' '}
                        {annotation.iscrowd ? t('是') : t('否')}
                      </div>
                      {annotation.segmentation && (
                        <div style={{ gridColumn: '1 / -1' }}>
                          <strong>{t('分割信息')}:</strong>
                          <div
                            style={{
                              maxHeight: 60,
                              overflowY: 'auto',
                              marginTop: 4,
                              padding: 4,
                              background: '#f8f8f8',
                              borderRadius: 4,
                            }}>
                            {JSON.stringify(annotation.segmentation)}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </Card>
          </div>
        )}
      </Modal>
    </>
  );
};

export default DataPart;
