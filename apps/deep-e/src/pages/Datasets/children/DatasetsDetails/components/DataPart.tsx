import React, { useEffect, useState } from 'react';
import { Table, Image, Modal, Card, Tag, Button } from 'antd';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { getDatasetDetail } from '../../../api';
import { DatasetDetailItem } from '../../../api/model';
import { t } from '@/languages';
import AnnotatedImage, {
  COCOAnnotation,
  COCOCategory,
  COCOImage,
} from './AnnotatedImage';

// Multimodal data structure types
interface MultimodalAnnotation {
  id: string;
  label: string;
  bboxes?: number[][];
  attributes?: Record<string, any>;
  segmentations?: number[][];
}

interface MultimodalAnnotationGroup {
  label: string;
  annotations: MultimodalAnnotation[];
}

interface MultimodalImageData {
  imageId: string;
  annotationGroups: MultimodalAnnotationGroup[];
}

interface MultimodalQAItem {
  question: string;
  answer: string;
  lang: string;
  type: string;
}

interface MultimodalDatasetDetailItem {
  qa: MultimodalQAItem[];
  images1?: string; // Base64 encoded image
  images1Name?: MultimodalImageData;
  images2?: string; // Base64 encoded image
  images2Name?: MultimodalImageData;
  [key: string]: any; // For additional image fields
}

const calculateContentLength = (value: any): number => {
  if (typeof value === 'string') return value.length;
  if (typeof value === 'number') return String(value).length;
  return JSON.stringify(value).length;
};

type DataPartProps = {
  datasetUuid: string;
  datasetType: number;
  changTotalData: (total: number) => void;
};

const DataPart: React.FC<DataPartProps> = ({
  datasetUuid,
  changTotalData,
  datasetType,
}) => {
  const [dataList, setDataList] = useState<DatasetDetailItem[]>([]);
  const [multimodalData, setMultimodalData] = useState<
    MultimodalDatasetDetailItem[]
  >([]);
  const [columnNames, setColumnNames] = useState<string[]>([]);

  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // Modal相关状态
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedImageData, setSelectedImageData] = useState<{
    imageUrl: string;
    annotations: COCOAnnotation[];
    categories: COCOCategory[];
    imageInfo: COCOImage;
  } | null>(null);

  // 图片翻动相关状态
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [currentRowData, setCurrentRowData] =
    useState<MultimodalDatasetDetailItem | null>(null);
  const [imageList, setImageList] = useState<
    Array<{
      columnName: string;
      imageUrl: string;
      annotationData: MultimodalImageData;
    }>
  >([]);

  // 从多模态标注数据生成COCO格式标注数据
  const generateCOCODataFromMultimodalAnnotation = (
    imageUrl: string,
    annotationData: MultimodalImageData,
  ) => {
    const annotationGroups = annotationData.annotationGroups || [];

    // 从标注组中提取所有唯一的标签作为类别
    const uniqueLabels = new Set<string>();
    annotationGroups.forEach((group) => {
      group.annotations.forEach((ann) => {
        uniqueLabels.add(ann.label);
      });
    });

    const mockCategories: COCOCategory[] = Array.from(uniqueLabels).map(
      (label, index) => ({
        id: index + 1,
        name: label,
        supercategory:
          annotationGroups.find((group) =>
            group.annotations.some((ann) => ann.label === label),
          )?.label || 'object',
      }),
    );

    const mockImageInfo: COCOImage = {
      id: 1,
      file_name: annotationData.imageId || 'multimodal_image.jpg',
      width: 640,
      height: 480,
      url: imageUrl,
    };

    // 将多模态格式的标注转换为COCO格式
    const mockAnnotations: COCOAnnotation[] = [];
    let annotationId = 1;

    annotationGroups.forEach((group) => {
      group.annotations.forEach((annotation) => {
        const categoryId =
          mockCategories.findIndex((cat) => cat.name === annotation.label) + 1;

        if (annotation.bboxes) {
          annotation.bboxes.forEach((bbox) => {
            // 假设bbox格式为 [x, y, width, height]
            const [x, y, width, height] = bbox;

            mockAnnotations.push({
              id: annotationId++,
              image_id: 1,
              category_id: categoryId,
              bbox: [x, y, width, height],
              area: width * height,
              iscrowd: 0,
              segmentation: annotation.segmentations || [
                [x, y, x + width, y, x + width, y + height, x, y + height],
              ],
            });
          });
        }
      });
    });

    return {
      imageUrl,
      annotations: mockAnnotations,
      categories: mockCategories,
      imageInfo: mockImageInfo,
    };
  };

  // 处理多模态图片点击事件
  const handleMultimodalImageClick = (
    clickedImageUrl: string,
    clickedAnnotationData: MultimodalImageData,
    record: MultimodalDatasetDetailItem,
    event: React.MouseEvent,
  ) => {
    event.stopPropagation(); // 阻止事件冒泡，避免触发行点击

    // 收集当前行的所有图片信息
    const images: Array<{
      columnName: string;
      imageUrl: string;
      annotationData: MultimodalImageData;
    }> = [];

    // 遍历columnNames，找到所有图片列
    columnNames.forEach((columnName) => {
      if (columnName.startsWith('images') && !columnName.endsWith('Name')) {
        const imageData = record[columnName];
        const annotationKey = `${columnName}Name`;
        const annotationData = record[annotationKey] as MultimodalImageData;

        if (imageData && typeof imageData === 'string') {
          images.push({
            columnName,
            imageUrl: `data:image/jpeg;base64,${imageData}`,
            annotationData: annotationData || {
              imageId: `${columnName}_image`,
              annotationGroups: [],
            },
          });
        }
      }
    });

    // 找到当前点击图片的索引
    const clickedIndex = images.findIndex(
      (img) => img.imageUrl === clickedImageUrl,
    );

    console.log('收集到的图片列表:', images);
    console.log('columnNames:', columnNames);
    console.log('record:', record);

    setImageList(images);
    setCurrentRowData(record);
    setCurrentImageIndex(clickedIndex >= 0 ? clickedIndex : 0);

    // 设置当前图片的标注数据
    const currentImage = images[clickedIndex >= 0 ? clickedIndex : 0];
    if (currentImage) {
      const cocoData = generateCOCODataFromMultimodalAnnotation(
        currentImage.imageUrl,
        currentImage.annotationData,
      );
      setSelectedImageData(cocoData);
    } else {
      // 如果没有找到图片，使用点击的图片数据
      const cocoData = generateCOCODataFromMultimodalAnnotation(
        clickedImageUrl,
        clickedAnnotationData || {
          imageId: 'unknown_image',
          annotationGroups: [],
        },
      );
      setSelectedImageData(cocoData);
    }

    setModalVisible(true);
  };

  // 切换到上一张图片
  const handlePreviousImage = () => {
    if (imageList.length <= 1) return;

    const newIndex =
      currentImageIndex > 0 ? currentImageIndex - 1 : imageList.length - 1;
    setCurrentImageIndex(newIndex);

    const currentImage = imageList[newIndex];
    if (currentImage) {
      const cocoData = generateCOCODataFromMultimodalAnnotation(
        currentImage.imageUrl,
        currentImage.annotationData,
      );
      setSelectedImageData(cocoData);
    }
  };

  // 切换到下一张图片
  const handleNextImage = () => {
    if (imageList.length <= 1) return;

    const newIndex =
      currentImageIndex < imageList.length - 1 ? currentImageIndex + 1 : 0;
    setCurrentImageIndex(newIndex);

    const currentImage = imageList[newIndex];
    if (currentImage) {
      const cocoData = generateCOCODataFromMultimodalAnnotation(
        currentImage.imageUrl,
        currentImage.annotationData,
      );
      setSelectedImageData(cocoData);
    }
  };

  const fetchDetail = async (page = 1, pageSize = 10) => {
    if (!datasetUuid) return;

    setLoading(true);
    try {
      const res = await getDatasetDetail({
        datasetUuid,
        page,
        pageSize,
        datasetType,
      });
      if (res.code === 0) {
        if (datasetType === 2) {
          // Multimodal dataset
          const multimodalResponse = res.data as any;
          console.log('多模态数据响应:', multimodalResponse);
          if (multimodalResponse.list) {
            const columnNameList = multimodalResponse.list.columnNameList || [];
            const datasetDetailList =
              multimodalResponse.list.datasetDetailList || [];
            console.log('columnNameList:', columnNameList);
            console.log('datasetDetailList:', datasetDetailList);

            setColumnNames(columnNameList);
            setMultimodalData(datasetDetailList);
            setPagination({
              current: multimodalResponse.page || 1,
              pageSize: multimodalResponse.pageSize || 10,
              total: multimodalResponse.total || 0,
            });
            changTotalData(multimodalResponse.total || 0);
          }
        } else {
          // Single-modal dataset (datasetType === 1)
          const list = Array.isArray(res.data) ? res.data : res.data.list;
          setDataList(list || []);

          if (res.data.page) {
            setPagination({
              current: res.data.page,
              pageSize: res.data.pageSize || 10,
              total: res.data.total || 0,
            });
            changTotalData(res.data.total || 0);
          }
        }
      }
    } catch (error) {
      console.error(t('获取数据集详情失败：'), error);
    } finally {
      setLoading(false);
    }
  };

  const handleTableChange = (newPagination: any) => {
    fetchDetail(newPagination.current, newPagination.pageSize);
  };
  const handleRowClick = (
    record: DatasetDetailItem | MultimodalDatasetDetailItem,
    index: number,
  ) => {
    const recordKey = 'id' in record ? String(record.id) : String(index);
    setExpandedRowKeys((prev) =>
      prev.includes(recordKey)
        ? prev.filter((key) => key !== recordKey)
        : [...prev, recordKey],
    );
  };

  // Generate multimodal table columns
  const generateMultimodalColumns =
    (): ColumnsType<MultimodalDatasetDetailItem> => {
      const baseColumns = [
        {
          title: t('序号'),
          dataIndex: 'index',
          key: 'index',
          width: 80,
          align: 'center' as const,
          render: (_: unknown, __: unknown, index: number) => index + 1,
        },
      ];

      if (!columnNames.length) return baseColumns;

      // 严格按照 columnNameList 的顺序生成列
      const dynamicColumns = columnNames.map((columnName) => ({
        title: getColumnTitle(columnName),
        dataIndex: columnName,
        key: columnName,
        width: getColumnWidth(columnName),
        render: (
          value: any,
          record: MultimodalDatasetDetailItem,
          index: number,
        ) => {
          const isExpanded = expandedRowKeys.includes(String(index));
          return renderMultimodalCell(
            columnName,
            value,
            record,
            isExpanded,
            index,
          );
        },
      }));

      return [...baseColumns, ...dynamicColumns];
    };

  // Get column title for multimodal data
  const getColumnTitle = (columnName: string): string => {
    const titleMap: Record<string, string> = {
      qa: t('问答对'),
      images1: t('图片1'),
      images1Name: t('图片1标注'),
      images2: t('图片2'),
      images2Name: t('图片2标注'),
    };
    return titleMap[columnName] || columnName;
  };

  // Get column width for multimodal data
  const getColumnWidth = (columnName: string): number => {
    const widthMap: Record<string, number> = {
      qa: 300,
      images1: 150,
      images1Name: 200,
      images2: 150,
      images2Name: 200,
    };
    return widthMap[columnName] || 150;
  };

  // Render multimodal cell content
  const renderMultimodalCell = (
    columnName: string,
    value: any,
    record: MultimodalDatasetDetailItem,
    isExpanded: boolean,
    _index: number,
  ) => {
    if (columnName === 'qa') {
      if (!value || !Array.isArray(value)) return '-';
      return (
        <div
          className={`${isExpanded ? 'max-h-none' : 'max-h-[200px]'} overflow-auto transition-all duration-200`}
          style={{
            fontFamily: 'monospace',
            fontSize: '12px',
            backgroundColor: '#f5f5f5',
            padding: '8px',
            borderRadius: '4px',
            whiteSpace: 'pre-wrap',
          }}>
          {JSON.stringify(value, null, 2)}
        </div>
      );
    }

    if (columnName.startsWith('images') && columnName.endsWith('Name')) {
      // Handle annotation data (images1Name, images2Name, etc.) - 显示完整JSON
      if (!value || typeof value !== 'object') return '-';
      return (
        <div
          className={`${isExpanded ? 'max-h-none' : 'max-h-[200px]'} overflow-auto transition-all duration-200`}
          style={{
            fontFamily: 'monospace',
            fontSize: '12px',
            backgroundColor: '#f5f5f5',
            padding: '8px',
            borderRadius: '4px',
            whiteSpace: 'pre-wrap',
          }}>
          {JSON.stringify(value, null, 2)}
        </div>
      );
    }

    if (columnName.startsWith('images') && !columnName.endsWith('Name')) {
      // Handle base64 image data (images1, images2, etc.)
      if (!value || typeof value !== 'string') return '-';
      const imageUrl = `data:image/jpeg;base64,${value}`;

      // Get corresponding annotation data
      const annotationKey = `${columnName}Name`;
      const annotationData = record[annotationKey] as MultimodalImageData;

      return (
        <Image
          src={imageUrl}
          alt={columnName}
          style={{
            width: 100,
            height: 75,
            objectFit: 'cover',
            borderRadius: 4,
            cursor: 'pointer',
          }}
          placeholder={
            <div
              style={{
                width: 100,
                height: 75,
                background: '#f0f0f0',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              {t('加载中')}
            </div>
          }
          preview={false}
          onClick={(event) =>
            handleMultimodalImageClick(imageUrl, annotationData, record, event)
          }
        />
      );
    }

    // Default rendering for other fields
    if (typeof value === 'string' || typeof value === 'number') {
      return (
        <div
          className={`${isExpanded ? 'max-h-none' : 'max-h-[100px]'} overflow-hidden transition-all duration-200`}>
          {value}
        </div>
      );
    }

    return (
      <div
        className={`${isExpanded ? 'max-h-none' : 'max-h-[100px]'} overflow-hidden transition-all duration-200`}>
        {JSON.stringify(value)}
      </div>
    );
  };

  // Generate single-modal table columns (existing logic)
  const generateSingleModalColumns = (): ColumnsType<DatasetDetailItem> => {
    if (!dataList.length) return [];

    // 计算每列内容的最大长度
    const columnLengths: Record<string, number> = {};
    const baseColumns = [
      {
        title: t('序号'),
        dataIndex: 'index',
        key: 'index',
        width: 80,
        align: 'center' as const,
        render: (_: unknown, __: unknown, index: number) => index + 1,
      },
    ];

    if (!dataList[0]?.datasetDetail) return baseColumns;

    // 计算每列内容的最大长度
    Object.keys(dataList[0].datasetDetail).forEach((key) => {
      columnLengths[key] = dataList.reduce((maxLength, item) => {
        const contentLength = calculateContentLength(item.datasetDetail[key]);
        return Math.max(maxLength, contentLength);
      }, key.length);
    });

    // 计算总长度和比例
    const totalLength = Object.values(columnLengths).reduce(
      (sum, len) => sum + len,
      0,
    );
    const tableWidth = window.innerWidth - 200; // 预留边距和滚动条空间

    // 定义友好的列标题映射
    const columnTitleMap: Record<string, string> = {
      messages: t('对话消息'),
    };

    const dynamicColumns = Object.keys(dataList[0].datasetDetail).map(
      (key) => ({
        title: columnTitleMap[key] || key,
        dataIndex: ['datasetDetail', key],
        key,
        width: Math.max(
          100,
          Math.floor((columnLengths[key] / totalLength) * tableWidth),
        ),
        render: (value: any, record: DatasetDetailItem) => {
          const isExpanded = expandedRowKeys.includes(record.id.toString());

          // 特殊处理新数据结构的字段
          if (key === 'qa') {
            // qa字段直接显示为格式化的JSON字符串
            return (
              <div
                className={`${isExpanded ? 'max-h-none' : 'max-h-[200px]'} overflow-auto transition-all duration-200`}
                style={{
                  fontFamily: 'monospace',
                  fontSize: '12px',
                  backgroundColor: '#f5f5f5',
                  padding: '8px',
                  borderRadius: '4px',
                  whiteSpace: 'pre-wrap',
                }}>
                {value}
              </div>
            );
          }

          if (typeof value === 'string' || typeof value === 'number') {
            return (
              <div
                className={`${isExpanded ? 'max-h-none' : 'max-h-[100px]'} overflow-hidden transition-all duration-200`}>
                {value}
              </div>
            );
          }
          return (
            <div
              className={`${isExpanded ? 'max-h-none' : 'max-h-[100px]'} overflow-hidden transition-all duration-200`}>
              {JSON.stringify(value)}
            </div>
          );
        },
      }),
    );

    return [...baseColumns, ...dynamicColumns];
  };

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!modalVisible || imageList.length <= 1) return;

      if (event.key === 'ArrowLeft') {
        event.preventDefault();
        handlePreviousImage();
      } else if (event.key === 'ArrowRight') {
        event.preventDefault();
        handleNextImage();
      }
    };

    if (modalVisible) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [modalVisible, currentImageIndex, imageList.length]);

  useEffect(() => {
    fetchDetail();
  }, []);

  return (
    <>
      {datasetType === 2 ? (
        <Table<MultimodalDatasetDetailItem>
          bordered
          rowKey={(_, index) => String(index)}
          columns={generateMultimodalColumns()}
          dataSource={multimodalData}
          loading={loading}
          pagination={pagination}
          onChange={handleTableChange}
          onRow={(record, index) => ({
            onClick: () => handleRowClick(record, index || 0),
            style: { cursor: 'pointer' },
          })}
          scroll={{ x: 'max-content' }}
        />
      ) : (
        <Table<DatasetDetailItem>
          bordered
          rowKey='id'
          columns={generateSingleModalColumns()}
          dataSource={dataList}
          loading={loading}
          pagination={pagination}
          onChange={handleTableChange}
          onRow={(record, index) => ({
            onClick: () => handleRowClick(record, index || 0),
            style: { cursor: 'pointer' },
          })}
          scroll={{ x: 'max-content' }}
        />
      )}

      {/* COCO标注信息Modal */}
      <Modal
        title={
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
            <span>{t('图片标注信息 (COCO格式)')}</span>
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <Button
                type='text'
                icon={<LeftOutlined />}
                onClick={handlePreviousImage}
                disabled={imageList.length <= 1}
              />
              <span style={{ fontSize: '14px', color: '#666' }}>
                {imageList.length > 0
                  ? `${currentImageIndex + 1} / ${imageList.length}`
                  : '1 / 1'}
              </span>
              <Button
                type='text'
                icon={<RightOutlined />}
                onClick={handleNextImage}
                disabled={imageList.length <= 1}
              />
            </div>
          </div>
        }
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}>
        {selectedImageData && (
          <div style={{ maxHeight: '70vh', overflowY: 'auto' }}>
            {/* 图片标注可视化 */}
            <Card
              title={
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                  }}>
                  <span>{t('图片标注可视化')}</span>
                  {imageList.length > 0 && imageList[currentImageIndex] && (
                    <Tag color='blue'>
                      {getColumnTitle(imageList[currentImageIndex].columnName)}
                    </Tag>
                  )}
                </div>
              }
              size='small'
              style={{ marginBottom: 16 }}>
              <div style={{ display: 'flex', gap: 16 }}>
                <div style={{ flex: 2 }}>
                  <AnnotatedImage
                    imageUrl={selectedImageData.imageUrl}
                    annotations={selectedImageData.annotations}
                    categories={selectedImageData.categories}
                    imageInfo={selectedImageData.imageInfo}
                    maxWidth={600}
                    showControls={true}
                  />
                </div>
                <div style={{ flex: 1 }}>
                  <div style={{ marginBottom: 16 }}>
                    <p>
                      <strong>{t('文件名')}:</strong>{' '}
                      {selectedImageData.imageInfo.file_name}
                    </p>
                    <p>
                      <strong>{t('尺寸')}:</strong>{' '}
                      {selectedImageData.imageInfo.width} ×{' '}
                      {selectedImageData.imageInfo.height}
                    </p>
                    <p>
                      <strong>ID:</strong> {selectedImageData.imageInfo.id}
                    </p>
                  </div>
                </div>
              </div>
            </Card>

            {/* 类别信息 */}
            <Card
              title={t('标注类别')}
              size='small'
              style={{ marginBottom: 16 }}>
              <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>
                {selectedImageData.categories.map((category) => (
                  <Tag key={category.id} color='blue'>
                    {category.name} (ID: {category.id})
                  </Tag>
                ))}
              </div>
            </Card>

            {/* 标注信息 */}
            <Card title={t('标注详情')} size='small'>
              {selectedImageData.annotations.map((annotation, index) => {
                const category = selectedImageData.categories.find(
                  (cat) => cat.id === annotation.category_id,
                );
                return (
                  <div
                    key={annotation.id}
                    style={{
                      marginBottom: 16,
                      padding: 12,
                      border: '1px solid #f0f0f0',
                      borderRadius: 6,
                    }}>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        marginBottom: 8,
                      }}>
                      <Tag color='green'>
                        {t('标注')} {index + 1}
                      </Tag>
                      <Tag color='orange'>
                        {category?.name || t('未知类别')}
                      </Tag>
                    </div>
                    <div
                      style={{
                        display: 'grid',
                        gridTemplateColumns: '1fr 1fr',
                        gap: 8,
                        fontSize: '12px',
                      }}>
                      <div>
                        <strong>{t('标注ID')}:</strong> {annotation.id}
                      </div>
                      <div>
                        <strong>{t('类别ID')}:</strong> {annotation.category_id}
                      </div>
                      <div>
                        <strong>{t('边界框')}:</strong> [
                        {annotation.bbox.join(', ')}]
                      </div>
                      <div>
                        <strong>{t('面积')}:</strong> {annotation.area}
                      </div>
                      <div>
                        <strong>{t('是否群体')}:</strong>{' '}
                        {annotation.iscrowd ? t('是') : t('否')}
                      </div>
                      {annotation.segmentation && (
                        <div style={{ gridColumn: '1 / -1' }}>
                          <strong>{t('分割信息')}:</strong>
                          <div
                            style={{
                              maxHeight: 60,
                              overflowY: 'auto',
                              marginTop: 4,
                              padding: 4,
                              background: '#f8f8f8',
                              borderRadius: 4,
                            }}>
                            {JSON.stringify(annotation.segmentation)}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </Card>
          </div>
        )}
      </Modal>
    </>
  );
};

export default DataPart;
