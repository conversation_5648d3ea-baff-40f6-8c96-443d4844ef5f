import React, { useEffect, useState } from 'react';
import { Table, Modal, Card, Tag, Button } from 'antd';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { getDatasetDetail, getDatasetSingleOriginalImage } from '../../../api';
import { DatasetDetailItem } from '../../../api/model';
import { t } from '@/languages';
import { COCOAnnotation, COCOCategory, COCOImage } from './AnnotatedImage';

// Multimodal data structure types
interface MultimodalAnnotation {
  id: string;
  label: string;
  bboxes?: number[][];
  attributes?: Record<string, any>;
  segmentations?: number[][];
}

interface MultimodalAnnotationGroup {
  label: string;
  annotations: MultimodalAnnotation[];
}

interface MultimodalImageData {
  imageId: string;
  annotationGroups: MultimodalAnnotationGroup[];
}

interface MultimodalQAItem {
  question: string;
  answer: string;
  lang: string;
  type: string;
}

interface MultimodalDatasetDetailItem {
  qa: MultimodalQAItem[];
  images1?: string; // Base64 encoded image
  images1Name?: MultimodalImageData;
  images2?: string; // Base64 encoded image
  images2Name?: MultimodalImageData;
  [key: string]: any; // For additional image fields
}

const calculateContentLength = (value: any): number => {
  if (typeof value === 'string') return value.length;
  if (typeof value === 'number') return String(value).length;
  return JSON.stringify(value).length;
};

type DataPartProps = {
  datasetUuid: string;
  datasetType: number;
  changTotalData: (total: number) => void;
};

const DataPart: React.FC<DataPartProps> = ({
  datasetUuid,
  changTotalData,
  datasetType,
}) => {
  const [dataList, setDataList] = useState<DatasetDetailItem[]>([]);
  const [multimodalData, setMultimodalData] = useState<
    MultimodalDatasetDetailItem[]
  >([]);
  const [columnNames, setColumnNames] = useState<string[]>([]);

  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // Modal相关状态
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedImageData, setSelectedImageData] = useState<{
    imageUrl: string;
    annotations: COCOAnnotation[];
    categories: COCOCategory[];
    imageInfo: COCOImage;
  } | null>(null);

  // 图片翻动相关状态
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [currentRowData, setCurrentRowData] =
    useState<MultimodalDatasetDetailItem | null>(null);
  const [imageList, setImageList] = useState<
    Array<{
      columnName: string;
      imageId: string;
      imageUrl?: string; // 缓存的base64图片URL
      annotationData: MultimodalImageData;
    }>
  >([]);

  // 图片缓存（用于弹窗中的高清图片）
  const [imageCache, setImageCache] = useState<Record<string, string>>({});

  // 获取高清图片URL（用于弹窗显示）
  const getHighResImageUrl = async (imageId: string): Promise<string> => {
    // 先检查缓存
    if (imageCache[imageId]) {
      return imageCache[imageId];
    }

    try {
      const response = await getDatasetSingleOriginalImage({
        imageId,
        datasetUuid,
      });

      if (response.code === 0 && response.data.result) {
        const imageUrl = `data:image/jpeg;base64,${response.data.result}`;
        // 缓存图片
        setImageCache((prev) => ({
          ...prev,
          [imageId]: imageUrl,
        }));
        return imageUrl;
      }
    } catch (error) {
      console.error('获取高清图片失败:', error);
    }

    // 返回默认占位图片
    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9Ijc1IiB2aWV3Qm94PSIwIDAgMTAwIDc1IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iNzUiIGZpbGw9IiNmMGYwZjAiLz48dGV4dCB4PSI1MCIgeT0iNDAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZpbGw9IiM5OTkiPuWKoOi9veWkseaViTwvdGV4dD48L3N2Zz4=';
  };

  // 可缩放拖拽的图片组件
  const ZoomableImage: React.FC<{
    imageUrl: string;
    alt: string;
  }> = ({ imageUrl, alt }) => {
    const [scale, setScale] = useState(1);
    const [position, setPosition] = useState({ x: 0, y: 0 });
    const [isDragging, setIsDragging] = useState(false);
    const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
    const [imageLoaded, setImageLoaded] = useState(false);

    // 重置图片状态
    const resetImage = () => {
      setScale(1);
      setPosition({ x: 0, y: 0 });
    };

    // 处理滚轮缩放
    const handleWheel = (event: React.WheelEvent) => {
      event.preventDefault();
      const delta = event.deltaY > 0 ? -0.1 : 0.1;
      const newScale = Math.max(0.1, Math.min(5, scale + delta));
      setScale(newScale);
    };

    // 处理鼠标按下
    const handleMouseDown = (event: React.MouseEvent) => {
      if (event.button === 2) {
        // 右键
        event.preventDefault();
        setIsDragging(true);
        setDragStart({
          x: event.clientX - position.x,
          y: event.clientY - position.y,
        });
      }
    };

    // 处理鼠标移动
    const handleMouseMove = (event: React.MouseEvent) => {
      if (isDragging) {
        setPosition({
          x: event.clientX - dragStart.x,
          y: event.clientY - dragStart.y,
        });
      }
    };

    // 处理鼠标抬起
    const handleMouseUp = () => {
      setIsDragging(false);
    };

    // 阻止右键菜单
    const handleContextMenu = (event: React.MouseEvent) => {
      event.preventDefault();
    };

    // 当图片URL改变时重置状态
    useEffect(() => {
      resetImage();
      setImageLoaded(false);
    }, [imageUrl]);

    return (
      <div
        className='w-full h-full overflow-hidden relative flex items-center justify-center bg-gray-50'
        style={{
          cursor: isDragging ? 'grabbing' : 'grab',
        }}
        onWheel={handleWheel}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onContextMenu={handleContextMenu}>
        {!imageLoaded && (
          <div className='text-gray-500 text-base'>{t('加载中')}...</div>
        )}

        {/* 高清图片加载提示 */}
        {imageLoaded &&
          imageUrl.includes('base64') &&
          imageUrl.length < 1000 && (
            <div className='absolute top-2 left-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs'>
              {t('加载高清图片中')}...
            </div>
          )}

        <img
          src={imageUrl}
          alt={alt}
          className='max-w-none max-h-none select-none pointer-events-none'
          style={{
            transform: `translate(${position.x}px, ${position.y}px) scale(${scale})`,
            transition: isDragging ? 'none' : 'transform 0.1s ease-out',
            display: imageLoaded ? 'block' : 'none',
          }}
          onLoad={() => setImageLoaded(true)}
          onError={() => setImageLoaded(true)}
        />

        {/* 缩放控制提示 */}
        <div className='absolute bottom-2 right-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs pointer-events-none'>
          {Math.round(scale * 100)}% | 滚轮缩放 | 右键拖拽
        </div>
      </div>
    );
  };

  // 从多模态标注数据生成COCO格式标注数据
  const generateCOCODataFromMultimodalAnnotation = (
    imageUrl: string,
    annotationData: MultimodalImageData,
  ) => {
    const annotationGroups = annotationData.annotationGroups || [];

    // 从标注组中提取所有唯一的标签作为类别
    const uniqueLabels = new Set<string>();
    annotationGroups.forEach((group) => {
      group.annotations.forEach((ann) => {
        uniqueLabels.add(ann.label);
      });
    });

    const mockCategories: COCOCategory[] = Array.from(uniqueLabels).map(
      (label, index) => ({
        id: index + 1,
        name: label,
        supercategory:
          annotationGroups.find((group) =>
            group.annotations.some((ann) => ann.label === label),
          )?.label || 'object',
      }),
    );

    const mockImageInfo: COCOImage = {
      id: 1,
      file_name: annotationData.imageId || 'multimodal_image.jpg',
      width: 640,
      height: 480,
      url: imageUrl,
    };

    // 将多模态格式的标注转换为COCO格式
    const mockAnnotations: COCOAnnotation[] = [];
    let annotationId = 1;

    annotationGroups.forEach((group) => {
      group.annotations.forEach((annotation) => {
        const categoryId =
          mockCategories.findIndex((cat) => cat.name === annotation.label) + 1;

        if (annotation.bboxes) {
          annotation.bboxes.forEach((bbox) => {
            // 假设bbox格式为 [x, y, width, height]
            const [x, y, width, height] = bbox;

            mockAnnotations.push({
              id: annotationId++,
              image_id: 1,
              category_id: categoryId,
              bbox: [x, y, width, height],
              area: width * height,
              iscrowd: 0,
              segmentation: annotation.segmentations || [
                [x, y, x + width, y, x + width, y + height, x, y + height],
              ],
            });
          });
        }
      });
    });

    return {
      imageUrl,
      annotations: mockAnnotations,
      categories: mockCategories,
      imageInfo: mockImageInfo,
    };
  };

  // 处理多模态图片点击事件
  const handleMultimodalImageClick = async (
    clickedImageUrl: string,
    clickedAnnotationData: MultimodalImageData,
    record: MultimodalDatasetDetailItem,
    event: React.MouseEvent,
  ) => {
    event.stopPropagation(); // 阻止事件冒泡，避免触发行点击

    // 收集当前行的所有图片信息
    const images: Array<{
      columnName: string;
      imageId: string;
      imageUrl?: string;
      annotationData: MultimodalImageData;
    }> = [];

    // 遍历columnNames，找到所有图片列
    columnNames.forEach((columnName) => {
      if (columnName.startsWith('images') && !columnName.endsWith('Name')) {
        const imageData = record[columnName];
        const annotationKey = `${columnName}Name`;
        const annotationData = record[annotationKey] as MultimodalImageData;

        if (imageData && typeof imageData === 'string') {
          const thumbnailUrl = `data:image/jpeg;base64,${imageData}`; // 表格中的缩略图
          images.push({
            columnName,
            imageId: annotationData?.imageId || `${columnName}_image`,
            imageUrl: thumbnailUrl, // 暂时使用缩略图，弹窗时会获取高清图
            annotationData: annotationData || {
              imageId: `${columnName}_image`,
              annotationGroups: [],
            },
          });
        }
      }
    });

    // 找到当前点击图片的索引
    const clickedIndex = images.findIndex(
      (img) => img.imageUrl === clickedImageUrl,
    );

    setImageList(images);
    setCurrentRowData(record);
    setCurrentImageIndex(clickedIndex >= 0 ? clickedIndex : 0);

    // 设置当前图片的标注数据，先使用缩略图显示
    const currentImage = images[clickedIndex >= 0 ? clickedIndex : 0];
    if (currentImage) {
      // 先用缩略图显示，然后异步加载高清图
      const cocoData = generateCOCODataFromMultimodalAnnotation(
        currentImage.imageUrl || clickedImageUrl,
        currentImage.annotationData,
      );
      setSelectedImageData(cocoData);

      // 异步获取高清图片并更新
      getHighResImageUrl(currentImage.imageId).then((highResUrl) => {
        const updatedCocoData = generateCOCODataFromMultimodalAnnotation(
          highResUrl,
          currentImage.annotationData,
        );
        setSelectedImageData(updatedCocoData);
      });
    } else {
      // 如果没有找到图片，使用点击的图片数据
      const cocoData = generateCOCODataFromMultimodalAnnotation(
        clickedImageUrl,
        clickedAnnotationData || {
          imageId: 'unknown_image',
          annotationGroups: [],
        },
      );
      setSelectedImageData(cocoData);
    }

    setModalVisible(true);
  };

  // 切换到上一张图片
  const handlePreviousImage = async () => {
    if (imageList.length <= 1) return;

    const newIndex =
      currentImageIndex > 0 ? currentImageIndex - 1 : imageList.length - 1;
    setCurrentImageIndex(newIndex);

    const currentImage = imageList[newIndex];
    if (currentImage) {
      // 先用缩略图显示
      const cocoData = generateCOCODataFromMultimodalAnnotation(
        currentImage.imageUrl || '',
        currentImage.annotationData,
      );
      setSelectedImageData(cocoData);

      // 异步获取高清图片并更新
      try {
        const highResUrl = await getHighResImageUrl(currentImage.imageId);
        const updatedCocoData = generateCOCODataFromMultimodalAnnotation(
          highResUrl,
          currentImage.annotationData,
        );
        setSelectedImageData(updatedCocoData);
      } catch (error) {
        console.error('获取高清图片失败:', error);
      }
    }
  };

  // 切换到下一张图片
  const handleNextImage = async () => {
    if (imageList.length <= 1) return;

    const newIndex =
      currentImageIndex < imageList.length - 1 ? currentImageIndex + 1 : 0;
    setCurrentImageIndex(newIndex);

    const currentImage = imageList[newIndex];
    if (currentImage) {
      // 先用缩略图显示
      const cocoData = generateCOCODataFromMultimodalAnnotation(
        currentImage.imageUrl || '',
        currentImage.annotationData,
      );
      setSelectedImageData(cocoData);

      // 异步获取高清图片并更新
      try {
        const highResUrl = await getHighResImageUrl(currentImage.imageId);
        const updatedCocoData = generateCOCODataFromMultimodalAnnotation(
          highResUrl,
          currentImage.annotationData,
        );
        setSelectedImageData(updatedCocoData);
      } catch (error) {
        console.error('获取高清图片失败:', error);
      }
    }
  };

  const fetchDetail = async (page = 1, pageSize = 10) => {
    if (!datasetUuid) return;

    setLoading(true);
    try {
      const res = await getDatasetDetail({
        datasetUuid,
        page,
        pageSize,
        datasetType,
      });
      if (res.code === 0) {
        if (datasetType === 2) {
          // Multimodal dataset
          const multimodalResponse = res.data as any;
          if (multimodalResponse.list) {
            const columnNameList = multimodalResponse.list.columnNameList || [];
            const datasetDetailList =
              multimodalResponse.list.datasetDetailList || [];

            setColumnNames(columnNameList);
            setMultimodalData(datasetDetailList);
            setPagination({
              current: multimodalResponse.page || 1,
              pageSize: multimodalResponse.pageSize || 10,
              total: multimodalResponse.total || 0,
            });
            changTotalData(multimodalResponse.total || 0);
          }
        } else {
          // Single-modal dataset (datasetType === 1)
          const list = Array.isArray(res.data) ? res.data : res.data.list;
          setDataList(list || []);

          if (res.data.page) {
            setPagination({
              current: res.data.page,
              pageSize: res.data.pageSize || 10,
              total: res.data.total || 0,
            });
            changTotalData(res.data.total || 0);
          }
        }
      }
    } catch (error) {
      console.error(t('获取数据集详情失败：'), error);
    } finally {
      setLoading(false);
    }
  };

  const handleTableChange = (newPagination: any) => {
    fetchDetail(newPagination.current, newPagination.pageSize);
  };
  const handleRowClick = (
    record: DatasetDetailItem | MultimodalDatasetDetailItem,
    index: number,
  ) => {
    const recordKey = 'id' in record ? String(record.id) : String(index);
    setExpandedRowKeys((prev) =>
      prev.includes(recordKey)
        ? prev.filter((key) => key !== recordKey)
        : [...prev, recordKey],
    );
  };

  // Generate multimodal table columns
  const generateMultimodalColumns =
    (): ColumnsType<MultimodalDatasetDetailItem> => {
      const baseColumns = [
        {
          title: t('序号'),
          dataIndex: 'index',
          key: 'index',
          width: 80,
          align: 'center' as const,
          render: (_: unknown, __: unknown, index: number) => index + 1,
        },
      ];

      if (!columnNames.length) return baseColumns;

      // 严格按照 columnNameList 的顺序生成列
      const dynamicColumns = columnNames.map((columnName) => ({
        title: getColumnTitle(columnName),
        dataIndex: columnName,
        key: columnName,
        width: getColumnWidth(columnName),
        render: (
          value: any,
          record: MultimodalDatasetDetailItem,
          index: number,
        ) => {
          const isExpanded = expandedRowKeys.includes(String(index));
          return renderMultimodalCell(
            columnName,
            value,
            record,
            isExpanded,
            index,
          );
        },
      }));

      return [...baseColumns, ...dynamicColumns];
    };

  // Get column title for multimodal data
  const getColumnTitle = (columnName: string): string => {
    const titleMap: Record<string, string> = {
      qa: t('问答对'),
      images1: t('图片1'),
      images1Name: t('图片1标注'),
      images2: t('图片2'),
      images2Name: t('图片2标注'),
    };
    return titleMap[columnName] || columnName;
  };

  // Get column width for multimodal data
  const getColumnWidth = (columnName: string): number => {
    const widthMap: Record<string, number> = {
      qa: 300,
      images1: 150,
      images1Name: 200,
      images2: 150,
      images2Name: 200,
    };
    return widthMap[columnName] || 150;
  };

  // Render multimodal cell content
  const renderMultimodalCell = (
    columnName: string,
    value: any,
    record: MultimodalDatasetDetailItem,
    isExpanded: boolean,
    _index: number,
  ) => {
    if (columnName === 'qa') {
      if (!value || !Array.isArray(value)) return '-';
      return (
        <div
          className={`${isExpanded ? 'max-h-none' : 'max-h-[200px]'} overflow-auto transition-all duration-200`}
          style={{
            fontFamily: 'monospace',
            fontSize: '12px',
            backgroundColor: '#f5f5f5',
            padding: '8px',
            borderRadius: '4px',
            whiteSpace: 'pre-wrap',
          }}>
          {JSON.stringify(value, null, 2)}
        </div>
      );
    }

    if (columnName.startsWith('images') && columnName.endsWith('Name')) {
      // Handle annotation data (images1Name, images2Name, etc.) - 显示完整JSON
      if (!value || typeof value !== 'object') return '-';
      return (
        <div
          className={`${isExpanded ? 'max-h-none' : 'max-h-[200px]'} overflow-auto transition-all duration-200`}
          style={{
            fontFamily: 'monospace',
            fontSize: '12px',
            backgroundColor: '#f5f5f5',
            padding: '8px',
            borderRadius: '4px',
            whiteSpace: 'pre-wrap',
          }}>
          {JSON.stringify(value, null, 2)}
        </div>
      );
    }

    if (columnName.startsWith('images') && !columnName.endsWith('Name')) {
      // Handle image data - use base64 data from getDatasetDetail response
      if (!value || typeof value !== 'string') return '-';

      const annotationKey = `${columnName}Name`;
      const annotationData = record[annotationKey] as MultimodalImageData;
      const imageUrl = `data:image/jpeg;base64,${value}`;

      return (
        <div
          style={{
            width: 100,
            height: 75,
            borderRadius: 4,
            cursor: 'pointer',
            border: '1px solid #d9d9d9',
            overflow: 'hidden',
          }}
          onClick={(event) =>
            handleMultimodalImageClick(imageUrl, annotationData, record, event)
          }>
          <img
            src={imageUrl}
            alt={columnName}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
            }}
          />
        </div>
      );
    }

    // Default rendering for other fields
    if (typeof value === 'string' || typeof value === 'number') {
      return (
        <div
          className={`${isExpanded ? 'max-h-none' : 'max-h-[100px]'} overflow-hidden transition-all duration-200`}>
          {value}
        </div>
      );
    }

    return (
      <div
        className={`${isExpanded ? 'max-h-none' : 'max-h-[100px]'} overflow-hidden transition-all duration-200`}>
        {JSON.stringify(value)}
      </div>
    );
  };

  // Generate single-modal table columns (existing logic)
  const generateSingleModalColumns = (): ColumnsType<DatasetDetailItem> => {
    if (!dataList.length) return [];

    // 计算每列内容的最大长度
    const columnLengths: Record<string, number> = {};
    const baseColumns = [
      {
        title: t('序号'),
        dataIndex: 'index',
        key: 'index',
        width: 80,
        align: 'center' as const,
        render: (_: unknown, __: unknown, index: number) => index + 1,
      },
    ];

    if (!dataList[0]?.datasetDetail) return baseColumns;

    // 计算每列内容的最大长度
    Object.keys(dataList[0].datasetDetail).forEach((key) => {
      columnLengths[key] = dataList.reduce((maxLength, item) => {
        const contentLength = calculateContentLength(item.datasetDetail[key]);
        return Math.max(maxLength, contentLength);
      }, key.length);
    });

    // 计算总长度和比例
    const totalLength = Object.values(columnLengths).reduce(
      (sum, len) => sum + len,
      0,
    );
    const tableWidth = window.innerWidth - 200; // 预留边距和滚动条空间

    // 定义友好的列标题映射
    const columnTitleMap: Record<string, string> = {
      messages: t('对话消息'),
    };

    const dynamicColumns = Object.keys(dataList[0].datasetDetail).map(
      (key) => ({
        title: columnTitleMap[key] || key,
        dataIndex: ['datasetDetail', key],
        key,
        width: Math.max(
          100,
          Math.floor((columnLengths[key] / totalLength) * tableWidth),
        ),
        render: (value: any, record: DatasetDetailItem) => {
          const isExpanded = expandedRowKeys.includes(record.id.toString());

          // 特殊处理新数据结构的字段
          if (key === 'qa') {
            // qa字段直接显示为格式化的JSON字符串
            return (
              <div
                className={`${isExpanded ? 'max-h-none' : 'max-h-[200px]'} overflow-auto transition-all duration-200`}
                style={{
                  fontFamily: 'monospace',
                  fontSize: '12px',
                  backgroundColor: '#f5f5f5',
                  padding: '8px',
                  borderRadius: '4px',
                  whiteSpace: 'pre-wrap',
                }}>
                {value}
              </div>
            );
          }

          if (typeof value === 'string' || typeof value === 'number') {
            return (
              <div
                className={`${isExpanded ? 'max-h-none' : 'max-h-[100px]'} overflow-hidden transition-all duration-200`}>
                {value}
              </div>
            );
          }
          return (
            <div
              className={`${isExpanded ? 'max-h-none' : 'max-h-[100px]'} overflow-hidden transition-all duration-200`}>
              {JSON.stringify(value)}
            </div>
          );
        },
      }),
    );

    return [...baseColumns, ...dynamicColumns];
  };

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!modalVisible || imageList.length <= 1) return;

      if (event.key === 'ArrowLeft') {
        event.preventDefault();
        handlePreviousImage();
      } else if (event.key === 'ArrowRight') {
        event.preventDefault();
        handleNextImage();
      }
    };

    if (modalVisible) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [modalVisible, currentImageIndex, imageList.length]);

  useEffect(() => {
    fetchDetail();
  }, []);

  return (
    <>
      {datasetType === 2 ? (
        <Table<MultimodalDatasetDetailItem>
          bordered
          rowKey={(_, index) => String(index)}
          columns={generateMultimodalColumns()}
          dataSource={multimodalData}
          loading={loading}
          pagination={pagination}
          onChange={handleTableChange}
          onRow={(record, index) => ({
            onClick: () => handleRowClick(record, index || 0),
            style: { cursor: 'pointer' },
          })}
          scroll={{ x: 'max-content' }}
        />
      ) : (
        <Table<DatasetDetailItem>
          bordered
          rowKey='id'
          columns={generateSingleModalColumns()}
          dataSource={dataList}
          loading={loading}
          pagination={pagination}
          onChange={handleTableChange}
          onRow={(record, index) => ({
            onClick: () => handleRowClick(record, index || 0),
            style: { cursor: 'pointer' },
          })}
          scroll={{ x: 'max-content' }}
        />
      )}

      {/* COCO标注信息Modal */}
      <Modal
        title={
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
            <span>{t('图片标注信息 (COCO格式)')}</span>
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <Button
                type='text'
                icon={<LeftOutlined />}
                onClick={handlePreviousImage}
                disabled={imageList.length <= 1}
              />
              <span style={{ fontSize: '14px', color: '#666' }}>
                {imageList.length > 0
                  ? `${currentImageIndex + 1} / ${imageList.length}`
                  : '1 / 1'}
              </span>
              <Button
                type='text'
                icon={<RightOutlined />}
                onClick={handleNextImage}
                disabled={imageList.length <= 1}
              />
            </div>
          </div>
        }
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={1200}
        className='top-5'
        styles={{
          body: {
            maxHeight: '90vh',
            overflow: 'auto',
          },
        }}>
        {selectedImageData && (
          <div className='space-y-3'>
            {/* 图片信息栏 */}
            <div className='flex justify-between items-center px-4 py-2 bg-gray-50 rounded-md'>
              <div className='flex gap-4 text-sm'>
                <span>
                  <strong>{t('文件名')}:</strong>{' '}
                  {selectedImageData.imageInfo.file_name}
                </span>
                <span>
                  <strong>{t('尺寸')}:</strong>{' '}
                  {selectedImageData.imageInfo.width} ×{' '}
                  {selectedImageData.imageInfo.height}
                </span>
                <span>
                  <strong>ID:</strong> {selectedImageData.imageInfo.id}
                </span>
              </div>
              {imageList.length > 0 && imageList[currentImageIndex] && (
                <Tag color='blue'>
                  {getColumnTitle(imageList[currentImageIndex].columnName)}
                </Tag>
              )}
            </div>

            {/* 图片显示区域 - 固定宽度和高度 */}
            <div className='w-full h-[600px] border border-gray-300 rounded-md overflow-hidden relative bg-gray-50'>
              <ZoomableImage
                imageUrl={selectedImageData.imageUrl}
                alt={selectedImageData.imageInfo.file_name}
              />
            </div>

            {/* 标注信息区域 */}
            <Card title={t('标注信息')} size='small' className='w-full'>
              <div className='space-y-3'>
                {/* 类别信息 */}
                <div>
                  <h4 className='text-sm font-medium mb-2'>{t('类别信息')}</h4>
                  <div className='flex flex-wrap gap-1'>
                    {selectedImageData.categories.map((category) => (
                      <Tag key={category.id} color='green'>
                        {category.name}
                      </Tag>
                    ))}
                  </div>
                </div>

                {/* 标注详情 */}
                <div>
                  <h4 className='text-sm font-medium mb-2'>{t('标注详情')}</h4>
                  <div className='max-h-60 overflow-y-auto space-y-2'>
                    {selectedImageData.annotations.map((annotation) => (
                      <div
                        key={annotation.id}
                        className='p-2 border border-gray-100 rounded text-xs space-y-1'>
                        <div>
                          <strong>ID:</strong> {annotation.id}
                        </div>
                        <div>
                          <strong>{t('类别')}:</strong>{' '}
                          {selectedImageData.categories.find(
                            (c) => c.id === annotation.category_id,
                          )?.name || 'Unknown'}
                        </div>
                        <div>
                          <strong>Bbox:</strong> [{annotation.bbox.join(', ')}]
                        </div>
                        <div>
                          <strong>{t('面积')}:</strong> {annotation.area}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </Card>
          </div>
        )}
      </Modal>
    </>
  );
};

export default DataPart;
