import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Card, Descriptions, Tag } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { getDatasetBaseInfoByUuid } from '../../api';
import dayjs from 'dayjs';
import DataPart from './components/DataPart';
import LogPart from './components/LogPart';



type DatasetDetail = {
  datasetName: string;
  datasetDesc: string;
  datasetState: number;
  createdAt: number;
  updatedAt: number;
};

export default function DatasetsDetails() {
  const navigate = useNavigate();
  const [detail, setDetail] = useState<DatasetDetail>();
  const [searchParams] = useSearchParams();
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const { datasetUuid, type } = Object.fromEntries(searchParams.entries());

  
  const fetchDetailByUuid = async () => {
    setLoading(true);
    try {
      const datasetUuid = searchParams.get('datasetUuid');
      if (!datasetUuid) return;
      const res = await getDatasetBaseInfoByUuid(datasetUuid);
      if (res.code !== 0) {
        throw new Error(res.msg);
      }
      setDetail(res.data.result);
    } catch (error) {
      console.error(t('获取数据集详情失败：'), error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDetailByUuid();
  }, [searchParams]);

  const handleBack = () => {
    navigate(-1);
  };

  return (
    <div className='p-[24px] flex flex-col gap-2'>
      <div>
        <a
          onClick={handleBack}
          className='flex items-center text-gray-600 hover:text-gray-900 cursor-pointer'>
          <ArrowLeftOutlined className='mr-1' />
          {t('返回')}
        </a>
      </div>
      <Card loading={loading} title={t('数据集详情')} className='mb-4'>
        <Descriptions>
          <Descriptions.Item label={t('数据集名称')}>
            {detail?.datasetName}
          </Descriptions.Item>
          <Descriptions.Item label={t('数据集描述')}>
            {detail?.datasetDesc}
          </Descriptions.Item>
          <Descriptions.Item label={t('总数')}>{total}</Descriptions.Item>
          <Descriptions.Item label={t('状态')}>
            <Tag color={detail?.datasetState === 1 ? 'green' : 'red'}>
              {getDatasetStateName(detail?.datasetState || 0)}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label={t('创建时间')}>
            {detail?.createdAt &&
              dayjs.unix(detail.createdAt).format('YYYY-MM-DD HH:mm:ss')}
          </Descriptions.Item>
          <Descriptions.Item label={t('更新时间')}>
            {detail?.updatedAt &&
              dayjs.unix(detail.updatedAt).format('YYYY-MM-DD HH:mm:ss')}
          </Descriptions.Item>
        </Descriptions>
      </Card>
      <Card title={type === 'log' ? t('日志') : t('数据列表')} className='mb-4'>
        {type === 'log' ? (
          <LogPart datasetUuid={datasetUuid} />
        ) : (
          <DataPart
            datasetType={Number(datasetType)}
            datasetUuid={datasetUuid}
            changTotalData={(total: number) => setTotal(total)}
          />
        )}
      </Card>
    </div>
  );
}
