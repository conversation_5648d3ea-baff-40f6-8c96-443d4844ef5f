{"提示": {"en-US": "Tip", "de-DE": "<PERSON><PERSON><PERSON><PERSON>", "$files": ["src/hooks/useAlertDialog.tsx(1464-1470)", "src/pages/Setting/pages/TrainMethod/page.tsx(816-822)", "src/pages/Setting/pages/DeployEnv/components/DeployEnvCard.tsx(803-809)"], "$id": 287}, "确认": {"en-US": "Confirm", "de-DE": "Bestätigen", "$files": ["src/hooks/useAlertDialog.tsx(1837-1843)", "src/components/Layout/index.tsx(6157-6163)", "src/components/ui/delete-button.tsx(2019-2025)", "src/pages/Setting/pages/TrainMethod/page.tsx(930-936)", "src/pages/Setting/pages/DeployEnv/components/DeployEnvCard.tsx(912-918)"], "$id": 95}, "共{total}条": {"en-US": "Total:  {total} entries", "de-DE": "Insgesamt {total} Einträge", "$files": ["src/hooks/usePageData.ts(662-675)", "src/pages/KnowledgeHub/children/KnowledgeDetails/page.tsx(2717-2730)", "src/pages/ModelSetting/pages/ThirdPartyModel/page.tsx(8946-8959)"], "$id": 1474}, "未知错误": {"en-US": "Unknown error", "de-DE": "Unbekannter Fehler", "$files": ["src/components/ErrorBoundary/ErrorProcess.ts(1030-1038)"], "$id": 892}, "无系统内置默认方案": {"en-US": "No system built-in default plan", "de-DE": "Kein systemintegrierter Standardplan verfügbar", "$files": ["src/components/ErrorBoundary/ErrorReason.ts(32-45)"], "$id": 893}, "登录成功,请继续操作": {"en-US": "Login successful, please continue", "de-DE": "Anmeldung erfolgreich, bitte fahren <PERSON> fort", "$files": ["src/components/Layout/ConfirmLoginModal.tsx(922-936)"], "$id": 894}, "您的登录已过期，请重新登录": {"en-US": "Your login has expired, please log in again", "de-DE": "<PERSON>hre Anmeldung ist abgelaufen, bitte melden Sie sich erneut an", "$files": ["src/components/Layout/ConfirmLoginModal.tsx(1373-1390)"], "$id": 895}, "用户名": {"en-US": "Username", "de-DE": "<PERSON><PERSON><PERSON><PERSON>", "$files": ["src/components/Layout/ConfirmLoginModal.tsx(1572-1579)", "src/pages/Setting/pages/UserManagement/page.tsx(4866-4873)"], "$id": 332}, "密码": {"en-US": "Password", "de-DE": "Passwort", "$files": ["src/components/Layout/ConfirmLoginModal.tsx(1684-1690)", "src/pages/Setting/pages/UserManagement/page.tsx(5238-5244)"], "$id": 336}, "公司编码": {"en-US": "Company code", "de-DE": "Unternehmenscode", "$files": ["src/components/Layout/ConfirmLoginModal.tsx(1777-1785)"], "$id": 896}, "确定退出到登录页面吗": {"en-US": "Are you sure you want to exit to the login page?", "de-DE": "Sind <PERSON> sic<PERSON>, dass Sie zur Anmeldeseite zurückkehren möchten?", "$files": ["src/components/Layout/ConfirmLoginModal.tsx(1977-1991)"], "$id": 897}, "取消": {"en-US": "Cancel", "de-DE": "Abbrechen", "$files": ["src/components/Layout/ConfirmLoginModal.tsx(2020-2026)", "src/components/UploadAvatar/index.tsx(1792-1798)", "src/components/ui/delete-button.tsx(1838-1844)", "src/pages/KnowledgeHub/page.tsx(1755-1761)", "src/pages/ModelParameters/index.tsx(1008-1014)", "src/pages/ModelParameters/index.tsx(12325-12331)", "src/pages/ModelParameters/index.tsx(16339-16345)", "src/components/Layout/components/ChangePasswordModal.tsx(5157-5163)", "src/pages/KnowledgeHub/children/CreateKnowledge/page.tsx(3028-3034)", "src/pages/KnowledgeHub/children/UploadKnowledgeFile/page.tsx(2884-2890)", "src/pages/ModelSetting/pages/ModelBaseList/page.tsx(8247-8253)", "src/pages/Setting/pages/UserManagement/page.tsx(5821-5827)", "src/pages/Setting/pages/DeployEnv/page.tsx(5831-5837)", "src/pages/ModelSetting/pages/ThirdPartyModel/components/ConfigModel.tsx(5137-5143)"], "$id": 182}, "退出登录": {"en-US": "Log out", "de-DE": "Abmelden", "$files": ["src/components/Layout/ConfirmLoginModal.tsx(2285-2293)"], "$id": 898}, "再次登录}": {"en-US": "Log in again}", "de-DE": "<PERSON><PERSON><PERSON> anmelden}", "$files": ["src/components/Layout/ConfirmLoginModal.tsx(2524-2533)"], "$id": 899}, "修改密码": {"en-US": "Change password", "de-DE": "Passwort ändern", "$files": ["src/components/Layout/Header.tsx(1757-1765)", "src/pages/UserPage/UserSettingModal.tsx(941-949)", "src/pages/UserPage/index.tsx(4598-4606)", "src/components/Layout/components/ChangePasswordModal.tsx(2985-2993)"], "$id": 993}, "注销": {"en-US": "Log out", "de-DE": "Abmelden", "$files": ["src/components/Layout/Header.tsx(3109-3115)"], "$id": 94}, "正在登出...": {"en-US": "Logging out...", "de-DE": "Wird abgemeldet...", "$files": ["src/components/Layout/Header.tsx(3255-3266)"], "$id": 1632}, "登出成功": {"en-US": "Logout successful", "de-DE": "<PERSON><PERSON><PERSON><PERSON><PERSON> erfo<PERSON>g<PERSON>ich", "$files": ["src/components/Layout/Header.tsx(3425-3433)"], "$id": 1633}, "登出失败，请重试": {"en-US": "<PERSON><PERSON><PERSON> failed, please try again", "de-DE": "Ab<PERSON><PERSON>ng fehlgeschlagen, bitte erneut versuchen", "$files": ["src/components/Layout/Header.tsx(3885-3897)", "src/components/Layout/Header.tsx(4057-4069)"], "$id": 1634}, "下线提示": {"en-US": "Offline notification", "de-DE": "Offline-Benachrichtigung", "$files": ["src/components/Layout/index.tsx(6099-6107)"], "$id": 900}, "该账号已被其他用户登录, 您已下线, 请重新登录。": {"en-US": "This account has been logged in by another user, you have been logged off, please log in again.", "de-DE": "Dieses <PERSON> wurde von einem anderen Benutzer angemeldet. Sie wurden abgemeldet, bitte melden Sie sich erneut an.", "$files": ["src/components/Layout/index.tsx(6529-6558)"], "$id": 901}, "上一页": {"en-US": "Previous Page", "de-DE": "Vorherige Seite", "$files": ["src/components/Pagination/Pagination.tsx(2900-2907)"], "$id": 96}, "下一页": {"en-US": "Next Page", "de-DE": "Nächste Seite", "$files": ["src/components/Pagination/Pagination.tsx(4794-4801)"], "$id": 97}, "跳转至第": {"en-US": "Jump to Page", "de-DE": "<PERSON><PERSON><PERSON> zu <PERSON>", "$files": ["src/components/Pagination/Pagination.tsx(5146-5154)"], "$id": 98}, "页": {"en-US": "Page", "de-DE": "Seite", "$files": ["src/components/Pagination/Pagination.tsx(5526-5531)"], "$id": 99}, "跳转": {"en-US": "Jump", "de-DE": "Springen", "$files": ["src/components/Pagination/Pagination.tsx(5710-5716)"], "$id": 100}, "获取数据集列表失败：": {"en-US": "Failed to get the dataset list:", "de-DE": "Fehler beim Abrufen der Datensatzliste:", "$files": ["src/pages/Datasets/page.tsx(1376-1390)"], "$id": 933}, "删除数据集成功": {"en-US": "Successfully deleted the dataset", "de-DE": "Datensatz erfolgreich <PERSON>", "$files": ["src/pages/Datasets/page.tsx(1781-1792)"], "$id": 934}, "删除数据集失败：": {"en-US": "Failed to delete the dataset:", "de-DE": "Fehler beim Löschen des Datensatzes:", "$files": ["src/pages/Datasets/page.tsx(1948-1960)"], "$id": 935}, "获取列表失败": {"en-US": "Failed to get the list", "de-DE": "Fehler beim Abrufen der Liste", "$files": ["src/pages/Datasets/page.tsx(2193-2203)", "src/pages/KnowledgeHub/page.tsx(1315-1325)", "src/pages/KnowledgeHub/page.tsx(1379-1389)"], "$id": 936}, "名称": {"en-US": "Name", "de-DE": "Name", "$files": ["src/pages/Datasets/page.tsx(2561-2567)", "src/pages/Datasets/page.tsx(4167-4173)", "src/pages/KnowledgeHub/page.tsx(4138-4144)", "src/pages/ModelParameters/index.tsx(14391-14397)", "src/pages/ModelSetting/pages/ThirdPartyModel/components/filter-component.tsx(4391-4397)"], "$id": 108}, "描述": {"en-US": "Description", "de-DE": "Beschreibung", "$files": ["src/pages/Datasets/page.tsx(2654-2660)", "src/pages/ModelSetting/pages/ThirdPartyModel/components/ConfigModel.tsx(4440-4446)"], "$id": 109}, "状态": {"en-US": "Status", "de-DE": "Status", "$files": ["src/pages/Datasets/page.tsx(2747-2753)", "src/pages/Datasets/page.tsx(4275-4281)", "src/pages/FineTuningLog/page.tsx(3120-3126)", "src/pages/ModelEvaluation/page.tsx(3635-3641)", "src/pages/Datasets/children/DatasetsDetails/index.tsx(2345-2351)", "src/pages/KnowledgeHub/children/KnowledgeDetails/page.tsx(5396-5402)", "src/pages/ModelSetting/pages/ThirdPartyModel/page.tsx(2557-2563)", "src/pages/ModelSetting/pages/SavedModel/page.tsx(4150-4156)", "src/pages/ModelSetting/pages/SavedModel/pages/log/page.tsx(780-786)", "src/pages/ModelSetting/pages/TrainedModel/pages/log/page.tsx(941-947)"], "$id": 259}, "创建时间": {"en-US": "Created At", "de-DE": "Erstellungsdatum", "$files": ["src/pages/Datasets/page.tsx(3137-3145)", "src/pages/Datasets/children/DatasetsDetails/index.tsx(2575-2583)", "src/pages/ModelSetting/pages/DeployedModel/components/ExpandContent.tsx(2068-2076)", "src/pages/ModelSetting/pages/SavedModel/components/ExpandContent.tsx(2088-2096)"], "$id": 304}, "操作": {"en-US": "Action", "de-DE": "Aktion", "$files": ["src/pages/Datasets/page.tsx(3285-3291)", "src/pages/FineTuningLog/page.tsx(4415-4421)", "src/pages/KnowledgeHub/page.tsx(2929-2935)", "src/pages/ModelEvaluation/page.tsx(4367-4373)", "src/pages/ModelParameters/index.tsx(6413-6419)", "src/pages/ModelParameters/index.tsx(8302-8308)", "src/pages/KnowledgeHub/children/KnowledgeDetails/page.tsx(5788-5794)", "src/pages/ModelSetting/pages/DeployedModel/page.tsx(1486-1492)", "src/pages/ModelSetting/pages/ModelBaseList/page.tsx(3503-3509)", "src/pages/ModelSetting/pages/SavedModel/page.tsx(5042-5048)", "src/pages/Setting/pages/UserManagement/page.tsx(3914-3920)", "src/pages/ModelSetting/pages/TrainedModel/page.tsx(2507-2513)"], "$id": 102}, "查看": {"en-US": "View", "de-DE": "<PERSON><PERSON><PERSON>", "$files": ["src/pages/Datasets/page.tsx(3540-3546)", "src/pages/ModelEvaluation/page.tsx(4606-4612)", "src/pages/ModelParameters/index.tsx(6698-6704)"], "$id": 937}, "是否删除该数据集？": {"en-US": "Are you sure you want to delete this dataset?", "de-DE": "<PERSON><PERSON> dieser Datensatz wirklich gelöscht werden?", "$files": ["src/pages/Datasets/page.tsx(3619-3632)"], "$id": 938}, "该操作将永久删除该数据集，请谨慎操作。": {"en-US": "This operation will permanently delete the dataset, please proceed with caution.", "de-DE": "Diese Aktion wird den Datensatz dauerhaft löschen. Bitte vorsichtig handeln.", "$files": ["src/pages/Datasets/page.tsx(3667-3690)"], "$id": 939}, "日志": {"en-US": "Log", "de-DE": "Protokoll", "$files": ["src/pages/Datasets/page.tsx(3848-3854)", "src/pages/Datasets/children/DatasetsDetails/index.tsx(2987-2993)", "src/pages/ModelSetting/pages/SavedModel/page.tsx(5430-5436)", "src/pages/ModelSetting/pages/TrainedModel/page.tsx(3303-3309)"], "$id": 277}, "查询": {"en-US": "Search", "de-DE": "Abfragen", "$files": ["src/pages/Datasets/page.tsx(4747-4753)", "src/pages/FineTuningLog/page.tsx(10711-10717)", "src/pages/KnowledgeHub/page.tsx(4328-4334)", "src/pages/ModelEvaluation/page.tsx(8117-8123)", "src/pages/ModelSetting/pages/ModelBaseList/page.tsx(5391-5397)"], "$id": 931}, "重置": {"en-US": "Reset", "de-DE": "Z<PERSON>ücksetzen", "$files": ["src/pages/Datasets/page.tsx(4820-4826)", "src/pages/FineTuningLog/page.tsx(10789-10795)", "src/pages/KnowledgeHub/page.tsx(4401-4407)", "src/pages/ModelEvaluation/page.tsx(8198-8204)", "src/pages/ModelSetting/pages/ModelBaseList/page.tsx(5475-5481)"], "$id": 932}, "数据集列表": {"en-US": "Dataset List", "de-DE": "Datensatzliste", "$files": ["src/pages/Datasets/page.tsx(5016-5025)"], "$id": 940}, "上传数据集": {"en-US": "Upload New Dataset", "de-DE": "Datensatz hochladen", "$files": ["src/pages/Datasets/page.tsx(5353-5362)"], "$id": 941}, "删除": {"en-US": "Delete", "de-DE": "Löschen", "$files": ["src/components/ui/delete-button.tsx(827-833)", "src/pages/KnowledgeHub/page.tsx(1704-1710)", "src/pages/ModelParameters/index.tsx(1100-1106)"], "$id": 179}, "确认删除": {"en-US": "Confirm deletion", "de-DE": "Bestätigen Sie das Löschen", "$files": ["src/components/ui/delete-button.tsx(857-865)"], "$id": 180}, "您确定要删除吗？此操作无法撤销。": {"en-US": "Are you sure you want to delete? This action cannot be undone.", "de-DE": "Sind <PERSON> sicher, dass Si<PERSON> löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.", "$files": ["src/components/ui/delete-button.tsx(895-915)"], "$id": 181}, "已完成": {"en-US": "Completed", "de-DE": "Abgeschlossen", "$files": ["src/pages/FineTuningLog/page.tsx(1688-1695)", "src/pages/FineTuningLog/page.tsx(3376-3383)"], "$id": 902}, "失败": {"en-US": "Failed", "de-DE": "<PERSON><PERSON>", "$files": ["src/pages/FineTuningLog/page.tsx(1725-1731)", "src/pages/FineTuningLog/page.tsx(3486-3492)"], "$id": 917}, "原始模版": {"en-US": "Original template", "de-DE": "Originalvorlage", "$files": ["src/pages/FineTuningLog/page.tsx(1799-1807)"], "$id": 907}, "系统模版": {"en-US": "System template", "de-DE": "<PERSON><PERSON><PERSON>", "$files": ["src/pages/FineTuningLog/page.tsx(1837-1845)"], "$id": 908}, "他建模版": {"en-US": "Other-created template", "de-DE": "Extern erstellte Vorlage", "$files": ["src/pages/FineTuningLog/page.tsx(1875-1883)"], "$id": 909}, "自建模版": {"en-US": "Self-created template", "de-DE": "<PERSON><PERSON><PERSON>", "$files": ["src/pages/FineTuningLog/page.tsx(1913-1921)"], "$id": 910}, "获取用户列表失败": {"en-US": "Failed to get the user list", "de-DE": "Fehler beim Abrufen der Benutzerliste", "$files": ["src/pages/FineTuningLog/page.tsx(2379-2391)", "src/pages/FineTuningLog/page.tsx(2489-2501)"], "$id": 911}, "获取用户列表失败:": {"en-US": "Failed to get the user list:", "de-DE": "Fehler beim Abrufen der Benutzerliste:", "$files": ["src/pages/FineTuningLog/page.tsx(2445-2458)"], "$id": 912}, "任务ID": {"en-US": "Task ID", "de-DE": "Aufgaben-ID", "$files": ["src/pages/FineTuningLog/page.tsx(2605-2613)"], "$id": 913}, "训练类型": {"en-US": "Training Type", "de-DE": "Trainingsart", "$files": ["src/pages/FineTuningLog/page.tsx(2733-2741)"], "$id": 260}, "训练名称": {"en-US": "Training Name", "de-DE": "Trainingsname", "$files": ["src/pages/FineTuningLog/page.tsx(2820-2828)", "src/pages/FineTuningLog/page.tsx(10000-10008)", "src/pages/TaskDetail/index.tsx(360-368)", "src/pages/Setting/pages/TrainMethod/page.tsx(1679-1687)"], "$id": 261}, "创建人": {"en-US": "Created By", "de-DE": "<PERSON><PERSON><PERSON>", "$files": ["src/pages/FineTuningLog/page.tsx(2917-2924)", "src/pages/ModelEvaluation/page.tsx(2573-2580)"], "$id": 914}, "模板类型": {"en-US": "Template Type", "de-DE": "Vorlagenart", "$files": ["src/pages/FineTuningLog/page.tsx(3015-3023)"], "$id": 915}, "进行中": {"en-US": "In progress", "de-DE": "In Bearbeitung", "$files": ["src/pages/FineTuningLog/page.tsx(3430-3437)"], "$id": 916}, "警告": {"en-US": "Warning", "de-DE": "<PERSON><PERSON><PERSON>", "$files": ["src/pages/FineTuningLog/page.tsx(3542-3548)"], "$id": 1585}, "未知": {"en-US": "Unknown", "de-DE": "Unbekannt", "$files": ["src/pages/FineTuningLog/page.tsx(3598-3604)", "src/pages/Datasets/children/DatasetsDetails/index.tsx(546-552)"], "$id": 1518}, "开始时间": {"en-US": "Start Time", "de-DE": "Startzeit", "$files": ["src/pages/FineTuningLog/page.tsx(3985-3993)", "src/pages/FineTuningLog/page.tsx(9637-9645)", "src/pages/ModelEvaluation/page.tsx(4020-4028)", "src/pages/ModelSetting/pages/DeployedModel/page.tsx(1230-1238)", "src/pages/ModelSetting/pages/SavedModel/page.tsx(4786-4794)", "src/pages/ModelSetting/pages/TrainedModel/page.tsx(2215-2223)"], "$id": 370}, "结束时间": {"en-US": "End Time", "de-DE": "Endzeit", "$files": ["src/pages/FineTuningLog/page.tsx(4139-4147)", "src/pages/FineTuningLog/page.tsx(9648-9656)", "src/pages/ModelEvaluation/page.tsx(4178-4186)", "src/pages/ModelSetting/pages/DeployedModel/page.tsx(1360-1368)", "src/pages/ModelSetting/pages/SavedModel/page.tsx(4916-4924)", "src/pages/ModelSetting/pages/TrainedModel/page.tsx(2363-2371)"], "$id": 371}, "查看详情": {"en-US": "View Details", "de-DE": "Details anzeigen", "$files": ["src/pages/FineTuningLog/page.tsx(4674-4682)", "src/pages/ModelSetting/pages/TrainedModel/components/ExpandContent.tsx(1286-1294)"], "$id": 310}, "Invalid response format": {"en-US": "Invalid response format", "de-DE": "Ungültiges Antwortformat", "$files": ["src/pages/FineTuningLog/page.tsx(5862-5889)"], "$id": 918}, "请求失败": {"en-US": "Request failed", "de-DE": "Anforderung fehlgeschlagen", "$files": ["src/pages/FineTuningLog/page.tsx(6196-6204)"], "$id": 919}, "获取日志数据失败:": {"en-US": "Failed to retrieve log data:", "de-DE": "Fehler beim Abrufen der Protokolldaten:", "$files": ["src/pages/FineTuningLog/page.tsx(6258-6271)", "src/pages/FineTuningLog/page.tsx(8720-8733)"], "$id": 920}, "获取日志数据失败": {"en-US": "Failed to retrieve log data", "de-DE": "Fehler beim Abrufen der Protokolldaten", "$files": ["src/pages/FineTuningLog/page.tsx(6352-6364)"], "$id": 921}, "请至少选择两条记录进行对比": {"en-US": "Please select at least two records for comparison", "de-DE": "Bitte wählen Sie mindestens zwei Datensätze zum Vergleichen aus.", "$files": ["src/pages/FineTuningLog/page.tsx(6795-6812)"], "$id": 922}, "最多只能对比2条记录": {"en-US": "A maximum of two records can be compared", "de-DE": "Es können maximal 2 Datensätze verglichen werden.", "$files": ["src/pages/FineTuningLog/page.tsx(6893-6907)"], "$id": 923}, "时间范围": {"en-US": "Date Range", "de-DE": "Zeitbereich", "$files": ["src/pages/FineTuningLog/page.tsx(9532-9540)"], "$id": 924}, "执行任务状态": {"en-US": "Status", "de-DE": "Ausführungsaufgabenstatus", "$files": ["src/pages/FineTuningLog/page.tsx(9743-9753)"], "$id": 925}, "请选择状态": {"en-US": "Please select a status", "de-DE": "Bitte Status auswählen", "$files": ["src/pages/FineTuningLog/page.tsx(9907-9916)"], "$id": 926}, "请输入任务名称": {"en-US": "Please enter the task name", "de-DE": "<PERSON>te geben Si<PERSON> den Tasknamen ein", "$files": ["src/pages/FineTuningLog/page.tsx(10044-10055)"], "$id": 927}, "模版类型": {"en-US": "Template Type", "de-DE": "Vorlagentyp", "$files": ["src/pages/FineTuningLog/page.tsx(10132-10140)"], "$id": 928}, "请选择类型": {"en-US": "Please select a type", "de-DE": "Bitte Typ auswählen", "$files": ["src/pages/FineTuningLog/page.tsx(10297-10306)"], "$id": 929}, "用户": {"en-US": "User", "de-DE": "<PERSON><PERSON><PERSON>", "$files": ["src/pages/FineTuningLog/page.tsx(10389-10395)", "src/pages/Setting/pages/UserManagement/page.tsx(2833-2839)"], "$id": 328}, "请选择用户": {"en-US": "Please select a user", "de-DE": "Bitte Benutzer auswählen", "$files": ["src/pages/FineTuningLog/page.tsx(10483-10492)"], "$id": 930}, "微调任务历史": {"en-US": "Fine-tuning Task History", "de-DE": "Feinabstimmungsaufgabenverlauf", "$files": ["src/pages/FineTuningLog/page.tsx(11022-11032)"], "$id": 262}, "新建任务": {"en-US": "New Training Task", "de-DE": "Neue Aufgabe erstellen", "$files": ["src/pages/FineTuningLog/page.tsx(11394-11402)"], "$id": 263}, "对比日志": {"en-US": "Training Log Comparison", "de-DE": "Vergleichsprotokoll", "$files": ["src/pages/FineTuningLog/page.tsx(11591-11599)"], "$id": 264}, "数据仪表盘": {"en-US": "Data Dashboard", "de-DE": "Daten-Dashboard", "$files": ["src/pages/Home/index.tsx(569-578)"], "$id": 1586}, "数据概览与分析": {"en-US": "Data Overview and Analysis", "de-DE": "Datenübersicht und Analyse", "$files": ["src/pages/Home/index.tsx(637-648)"], "$id": 1587}, "删除后不可恢复，请谨慎操作。": {"en-US": "This operation is irreversible, please proceed with caution.", "de-DE": "Löschung ist nicht wiederherstellbar, bitte mit Vorsicht vorgehen.", "$files": ["src/pages/KnowledgeHub/page.tsx(1669-1687)"], "$id": 942}, "删除成功": {"en-US": "Delete successful", "de-DE": "Erfolg<PERSON><PERSON>", "$files": ["src/pages/KnowledgeHub/page.tsx(1944-1952)", "src/pages/Setting/pages/UserManagement/page.tsx(1404-1412)", "src/pages/ModelSetting/pages/TrainedModel/page.tsx(1232-1240)"], "$id": 323}, "删除失败": {"en-US": "Delete failed", "de-DE": "Löschen fehlgeschlagen", "$files": ["src/pages/KnowledgeHub/page.tsx(2037-2045)", "src/pages/KnowledgeHub/page.tsx(2107-2115)", "src/pages/Setting/pages/UserManagement/page.tsx(1536-1544)", "src/pages/ModelSetting/pages/TrainedModel/page.tsx(1292-1300)"], "$id": 324}, "知识库名称": {"en-US": "Knowledge Base Name", "de-DE": "Wissensbasisname", "$files": ["src/pages/KnowledgeHub/page.tsx(2258-2267)", "src/pages/KnowledgeHub/children/CreateKnowledge/page.tsx(2160-2169)"], "$id": 943}, "知识库描述": {"en-US": "Knowledge Base Description", "de-DE": "Wissensbasisbeschreibung", "$files": ["src/pages/KnowledgeHub/page.tsx(2348-2357)", "src/pages/KnowledgeHub/children/CreateKnowledge/page.tsx(2413-2422)"], "$id": 944}, "嵌入模型": {"en-US": "Embedding Model", "de-DE": "Einbettungsmodell", "$files": ["src/pages/KnowledgeHub/page.tsx(2438-2446)"], "$id": 945}, "文件数目": {"en-US": "File Number", "de-DE": "<PERSON><PERSON><PERSON> der Dateien", "$files": ["src/pages/KnowledgeHub/page.tsx(2529-2537)"], "$id": 946}, "向量模型": {"en-US": "Vector Model", "de-DE": "Vektormodell", "$files": ["src/pages/KnowledgeHub/page.tsx(2640-2648)", "src/pages/KnowledgeHub/children/CreateKnowledge/page.tsx(2689-2697)"], "$id": 947}, "更新时间": {"en-US": "Update time", "de-DE": "Aktualisierungszeit", "$files": ["src/pages/KnowledgeHub/page.tsx(2771-2779)", "src/pages/Datasets/children/DatasetsDetails/index.tsx(2762-2770)"], "$id": 948}, "编辑": {"en-US": "Edit", "de-DE": "<PERSON><PERSON><PERSON>", "$files": ["src/pages/KnowledgeHub/page.tsx(3298-3304)", "src/pages/KnowledgeHub/page.tsx(3350-3356)", "src/pages/ModelSetting/pages/ModelBaseList/page.tsx(3848-3854)", "src/pages/ModelSetting/pages/ThirdPartyModel/components/CardItem.tsx(3629-3635)"], "$id": 270}, "是否删除该知识库？": {"en-US": "Are you sure you want to delete this knowledge base?", "de-DE": "Möchten Sie diese Wissensbasis löschen?", "$files": ["src/pages/KnowledgeHub/page.tsx(3458-3471)"], "$id": 949}, "该操作将永久删除该知识库，请谨慎操作。": {"en-US": "This operation will permanently delete the knowledge base, please proceed with caution.", "de-DE": "Diese Aktion wird die Wissensbasis dauerhaft löschen, bitte mit Vorsicht vorgehen.", "$files": ["src/pages/KnowledgeHub/page.tsx(3506-3529)"], "$id": 950}, "创建知识库": {"en-US": "New Knowledge Base", "de-DE": "Wissens<PERSON><PERSON> er<PERSON>", "$files": ["src/pages/KnowledgeHub/page.tsx(3805-3814)", "src/pages/KnowledgeHub/children/CreateKnowledge/page.tsx(1936-1945)"], "$id": 951}, "知识库管理": {"en-US": "Knowledge Base List", "de-DE": "Wissensdatenbankverwaltung", "$files": ["src/pages/KnowledgeHub/page.tsx(4507-4516)"], "$id": 952}, "服务器返回错误": {"en-US": "Server returned an error", "de-DE": "Serverfehler zurückgegeben", "$files": ["src/pages/ModelEvaluation/page.tsx(1224-1235)", "src/pages/KnowledgeHub/children/KnowledgeDetails/page.tsx(1062-1073)", "src/pages/KnowledgeHub/children/KnowledgeDetails/page.tsx(1128-1139)"], "$id": 978}, "下载失败:": {"en-US": "Download failed:", "de-DE": "Download fehlgeschlagen:", "$files": ["src/pages/ModelEvaluation/page.tsx(2029-2038)"], "$id": 979}, "下载失败": {"en-US": "Download failed", "de-DE": "Download fehlgeschlagen", "$files": ["src/pages/ModelEvaluation/page.tsx(2059-2067)"], "$id": 980}, "评估类型": {"en-US": "Evaluation Type", "de-DE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "$files": ["src/pages/ModelEvaluation/page.tsx(2681-2689)", "src/pages/ModelEvaluation/page.tsx(7151-7159)"], "$id": 364}, "模型A": {"en-US": "Model A", "de-DE": "Modell A", "$files": ["src/pages/ModelEvaluation/page.tsx(2796-2803)", "src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultDetail.tsx(1734-1741)", "src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/components/CompareDetail.tsx(2258-2265)", "src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/components/CompareList.tsx(2450-2457)"], "$id": 981}, "模型B": {"en-US": "Model B", "de-DE": "Modell B", "$files": ["src/pages/ModelEvaluation/page.tsx(3086-3093)", "src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultDetail.tsx(1943-1950)", "src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/components/CompareDetail.tsx(2392-2399)", "src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/components/CompareList.tsx(2943-2950)"], "$id": 982}, "裁判模型": {"en-US": "Referee Model", "de-DE": "Schiedsrichtermodell", "$files": ["src/pages/ModelEvaluation/page.tsx(3309-3317)", "src/pages/ModelEvaluation/children/RunModelEvaluation/constant.ts(467-475)", "src/pages/ModelEvaluation/children/RunModelEvaluation/components/ModelInput.tsx(3629-3637)", "src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultDetail.tsx(2218-2226)", "src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultDetail.tsx(2656-2664)", "src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/components/CompareDetail.tsx(2539-2547)"], "$id": 983}, "数据集": {"en-US": "Dataset", "de-DE": "Datensatz", "$files": ["src/pages/ModelEvaluation/page.tsx(3541-3548)"], "$id": 984}, "下载结果": {"en-US": "Download Result", "de-DE": "Download-<PERSON><PERSON><PERSON><PERSON>", "$files": ["src/pages/ModelEvaluation/page.tsx(4761-4769)"], "$id": 985}, "模型A名称": {"en-US": "Model A", "de-DE": "Modell A Name", "$files": ["src/pages/ModelEvaluation/page.tsx(6457-6466)"], "$id": 365}, "模型B名称": {"en-US": "Model B", "de-DE": "Modell B Name", "$files": ["src/pages/ModelEvaluation/page.tsx(6629-6638)"], "$id": 366}, "裁判模型名称": {"en-US": "Referee Model", "de-DE": "Schiedsrichtermodellname", "$files": ["src/pages/ModelEvaluation/page.tsx(6801-6811)"], "$id": 367}, "数据集名称": {"en-US": "Dataset Name", "de-DE": "Datenbankname", "$files": ["src/pages/ModelEvaluation/page.tsx(6978-6987)", "src/pages/Datasets/children/DatasetsDetails/index.tsx(2044-2053)", "src/pages/Datasets/children/UploadDatasets/index.tsx(12103-12112)", "src/pages/Datasets/children/UploadDatasets/index.tsx(13982-13991)"], "$id": 1015}, "评估状态": {"en-US": "Status", "de-DE": "Bewertungsstatus", "$files": ["src/pages/ModelEvaluation/page.tsx(7579-7587)"], "$id": 369}, "模型评估": {"en-US": "Model Evaluation", "de-DE": "Modellevaluierung", "$files": ["src/pages/ModelEvaluation/page.tsx(8438-8446)", "src/pages/Home/components/model-evaluation-chart.tsx(1216-1224)"], "$id": 986}, "新增评估": {"en-US": "New Evaluation Task", "de-DE": "Neue Evaluierung hinzufügen", "$files": ["src/pages/ModelEvaluation/page.tsx(8615-8623)"], "$id": 987}, "确定": {"en-US": "Confirm", "de-DE": "Bestätigen", "$files": ["src/pages/ModelParameters/index.tsx(981-987)", "src/pages/ModelSetting/pages/ModelBaseList/page.tsx(8162-8168)", "src/pages/ModelSetting/pages/ThirdPartyModel/components/ConfigModel.tsx(5066-5072)"], "$id": 1064}, "请输入有效的整数，例如：123、-456、0": {"en-US": "Please enter a valid integer, for example: 123, -456, 0", "de-DE": "<PERSON>te geben Si<PERSON> eine gültige ganze Zahl e<PERSON>, zum Beispiel: 123, -456, 0", "$files": ["src/pages/ModelParameters/index.tsx(1929-1955)"], "$id": 1635}, "请输入有效的浮点数，例如：3.14、-2.5、1e-3": {"en-US": "Please enter a valid floating-point number, for example: 3.14, -2.5, 1e-3", "de-DE": "<PERSON>te geben Si<PERSON> eine gültige Fließkommazahl ein, zum Beispiel: 3.14, -2.5, 1e-3", "$files": ["src/pages/ModelParameters/index.tsx(1989-2020)"], "$id": 1636}, "请输入布尔值：true 或 false": {"en-US": "Please enter a boolean value: true or false", "de-DE": "<PERSON>te geben Sie einen booleschen Wert ein: true oder false", "$files": ["src/pages/ModelParameters/index.tsx(2053-2076)"], "$id": 1637}, "请输入非空字符串": {"en-US": "Please enter a non-empty string", "de-DE": "<PERSON>te geben Sie einen nicht leeren String ein", "$files": ["src/pages/ModelParameters/index.tsx(2111-2123)"], "$id": 1638}, "请输入有效的参数值": {"en-US": "Please enter a valid parameter value", "de-DE": "Bitte geben Si<PERSON> einen gültigen Parameterwert ein", "$files": ["src/pages/ModelParameters/index.tsx(2152-2165)"], "$id": 1639}, "例如：123、-456、0": {"en-US": "For example: 123, -456, 0", "de-DE": "Zum Beispiel: 123, -456, 0", "$files": ["src/pages/ModelParameters/index.tsx(2321-2338)"], "$id": 1640}, "例如：3.14、-2.5、1e-3": {"en-US": "For example: 3.14, -2.5, 1e-3", "de-DE": "Zum Beispiel: 3.14, -2.5, 1e-3", "$files": ["src/pages/ModelParameters/index.tsx(2372-2393)"], "$id": 1641}, "输入 true 或 false": {"en-US": "Enter true or false", "de-DE": "true oder false eingeben", "$files": ["src/pages/ModelParameters/index.tsx(2426-2445)"], "$id": 1642}, "输入任意文本": {"en-US": "Enter any text", "de-DE": "Beliebigen Text eingeben", "$files": ["src/pages/ModelParameters/index.tsx(2480-2490)"], "$id": 1643}, "请输入参数值": {"en-US": "Please enter the parameter value", "de-DE": "<PERSON>te geben Si<PERSON> den Parameterwert ein", "$files": ["src/pages/ModelParameters/index.tsx(2519-2529)", "src/pages/ModelParameters/index.tsx(19419-19429)"], "$id": 1644}, "参数文件名称": {"en-US": "Parameter file name", "de-DE": "Name der Parameterdatei", "$files": ["src/pages/ModelParameters/index.tsx(5817-5827)", "src/pages/ModelParameters/index.tsx(12661-12671)"], "$id": 1645}, "公司ID": {"en-US": "Company ID", "de-DE": "Unternehmens-ID", "$files": ["src/pages/ModelParameters/index.tsx(6139-6147)"], "$id": 1646}, "确定要删除参数文件\"{name}\"吗？": {"en-US": "Are you sure you want to delete the parameter file \"{name}\"?", "de-DE": "<PERSON>d <PERSON> sic<PERSON>, dass Sie die Parameterdatei \"{name}\" löschen möchten?", "$files": ["src/pages/ModelParameters/index.tsx(6849-6887)"], "$id": 1647}, "参数标签": {"en-US": "Parameter label", "de-DE": "Parameter-Tag", "$files": ["src/pages/ModelParameters/index.tsx(7073-7081)", "src/pages/ModelParameters/index.tsx(16857-16865)"], "$id": 1648}, "参数名": {"en-US": "Parameter name", "de-DE": "Parametername", "$files": ["src/pages/ModelParameters/index.tsx(7267-7274)", "src/pages/ModelParameters/index.tsx(17436-17443)"], "$id": 1649}, "参数值": {"en-US": "Parameter value", "de-DE": "Parameterwert", "$files": ["src/pages/ModelParameters/index.tsx(7514-7521)", "src/pages/ModelParameters/index.tsx(19303-19310)"], "$id": 1650}, "类型": {"en-US": "Type", "de-DE": "<PERSON><PERSON>", "$files": ["src/pages/ModelParameters/index.tsx(7687-7693)", "src/pages/ModelSetting/pages/ThirdPartyModel/page.tsx(2477-2483)"], "$id": 1582}, "确定要删除参数\"{name}\"吗？": {"en-US": "Are you sure you want to delete the parameter \"{name}\"?", "de-DE": "<PERSON>d <PERSON> sic<PERSON>, dass Si<PERSON> den Parameter \"{name}\" löschen möchten?", "$files": ["src/pages/ModelParameters/index.tsx(8536-8557)"], "$id": 1651}, "模型参数管理": {"en-US": "Model parameter management", "de-DE": "Modellparameterverwaltung", "$files": ["src/pages/ModelParameters/index.tsx(10932-10942)"], "$id": 1652}, "管理和配置模型训练参数文件": {"en-US": "Manage and configure model training parameter files", "de-DE": "Verwalten und Konfigurieren von Modelltrainings-Parameterdateien", "$files": ["src/pages/ModelParameters/index.tsx(11044-11061)"], "$id": 1653}, "新建参数文件": {"en-US": "Create new parameter file", "de-DE": "Neue Parameterdatei erstellen", "$files": ["src/pages/ModelParameters/index.tsx(11271-11281)", "src/pages/ModelParameters/index.tsx(12041-12051)"], "$id": 1654}, "创建": {"en-US": "Create", "de-DE": "<PERSON><PERSON><PERSON><PERSON>", "$files": ["src/pages/ModelParameters/index.tsx(12296-12302)"], "$id": 1655}, "请输入参数文件名称": {"en-US": "Please enter the parameter file name", "de-DE": "<PERSON>te geben Si<PERSON> den Namen der Parameterdatei ein", "$files": ["src/pages/ModelParameters/index.tsx(12780-12793)", "src/pages/ModelParameters/index.tsx(12922-12935)"], "$id": 1656}, "名称不能超过50个字符": {"en-US": "Name cannot exceed 50 characters", "de-DE": "Der Name darf nicht mehr als 50 Zeichen enthalten", "$files": ["src/pages/ModelParameters/index.tsx(12834-12849)"], "$id": 1657}, "参数文件详情": {"en-US": "Parameter file details", "de-DE": "Details der Parameterdatei", "$files": ["src/pages/ModelParameters/index.tsx(13347-13357)"], "$id": 1658}, "关闭": {"en-US": "Close", "de-DE": "Schließen", "$files": ["src/pages/ModelParameters/index.tsx(13709-13715)"], "$id": 1659}, "基本信息": {"en-US": "Basic information", "de-DE": "Grundinformationen", "$files": ["src/pages/ModelParameters/index.tsx(14182-14190)"], "$id": 1660}, "参数列表": {"en-US": "Parameter list", "de-DE": "Parameterliste", "$files": ["src/pages/ModelParameters/index.tsx(14958-14966)"], "$id": 1662}, "个参数": {"en-US": "Parameter", "de-DE": "Parameter", "$files": ["src/pages/ModelParameters/index.tsx(15075-15082)"], "$id": 1663}, "新增参数": {"en-US": "New Parameter", "de-DE": "Neuer Parameter hinzufügen", "$files": ["src/pages/ModelParameters/index.tsx(15373-15381)", "src/pages/ModelParameters/index.tsx(16033-16041)"], "$id": 1664}, "添加": {"en-US": "Add", "de-DE": "Hinzufügen", "$files": ["src/pages/ModelParameters/index.tsx(16310-16316)", "src/pages/Setting/pages/UserManagement/page.tsx(5712-5718)", "src/pages/ModelEvaluation/children/RunModelEvaluation/components/Prompts.tsx(5654-5660)"], "$id": 340}, "请输入参数标签": {"en-US": "Please enter the parameter label", "de-DE": "<PERSON>te geben Sie das Parameter-Tag ein", "$files": ["src/pages/ModelParameters/index.tsx(16982-16993)", "src/pages/ModelParameters/index.tsx(17130-17141)"], "$id": 1665}, "标签不能超过50个字符": {"en-US": "Label cannot exceed 50 characters", "de-DE": "Das Tag darf nicht mehr als 50 Zeichen enthalten", "$files": ["src/pages/ModelParameters/index.tsx(17036-17051)"], "$id": 1666}, "请输入参数名": {"en-US": "Please enter the parameter name", "de-DE": "<PERSON>te geben Si<PERSON> den Parameternamen ein", "$files": ["src/pages/ModelParameters/index.tsx(17560-17570)", "src/pages/ModelParameters/index.tsx(17912-17922)"], "$id": 1667}, "参数名不能超过50个字符": {"en-US": "Parameter name cannot exceed 50 characters", "de-DE": "Der Parameternamen darf nicht mehr als 50 Zeichen enthalten", "$files": ["src/pages/ModelParameters/index.tsx(17613-17629)"], "$id": 1668}, "参数名只能包含字母、数字和下划线，且不能以数字开头": {"en-US": "Parameter name can only contain letters, numbers, and underscores, and cannot start with a number", "de-DE": "Der Parameternamen darf nur Buchstaben, Zahlen und Unterstriche enthalten und darf nicht mit einer Zahl beginnen", "$files": ["src/pages/ModelParameters/index.tsx(17740-17792)"], "$id": 1669}, "参数类型": {"en-US": "Parameter type", "de-DE": "Parametertyp", "$files": ["src/pages/ModelParameters/index.tsx(18227-18235)"], "$id": 1670}, "请选择参数类型": {"en-US": "Please select the parameter type", "de-DE": "Bitte wählen Sie den Parametertyp aus", "$files": ["src/pages/ModelParameters/index.tsx(18327-18338)", "src/pages/ModelParameters/index.tsx(18396-18407)"], "$id": 1671}, "参数值不能超过200个字符": {"en-US": "Parameter value cannot exceed 200 characters", "de-DE": "Der Parameterwert darf nicht mehr als 200 Zeichen enthalten", "$files": ["src/pages/ModelParameters/index.tsx(19471-19488)"], "$id": 1672}, "请先选择参数类型": {"en-US": "Please select the parameter type first", "de-DE": "Bitte wählen Sie zu<PERSON>t den Parametertyp aus", "$files": ["src/pages/ModelParameters/index.tsx(19753-19765)", "src/pages/ModelParameters/index.tsx(20320-20332)"], "$id": 1673}, "你是谁？": {"en-US": "Who are you?", "de-DE": "Wer bist du?", "$files": ["src/pages/PromptWords/constants.tsx(172-180)"], "$id": 953}, "解释一下勾股定理": {"en-US": "Explain the Pythagorean theorem", "de-DE": "Erkläre den Satz des Pythagoras", "$files": ["src/pages/PromptWords/constants.tsx(280-292)"], "$id": 954}, "请选择模型": {"en-US": "Please select a model", "de-DE": "<PERSON>te wählen Si<PERSON> ein Modell aus", "$files": ["src/pages/PromptWords/page.tsx(2991-3000)", "src/pages/ModelSetting/pages/ThirdPartyModel/components/ConfigModel.tsx(3840-3849)"], "$id": 955}, "请先选择模型才能进行对话": {"en-US": "Please select a model before engaging in a conversation", "de-DE": "Bitte wählen Sie das Modell aus, um den Dialog zu führen.", "$files": ["src/pages/PromptWords/page.tsx(3108-3124)"], "$id": 1792}, "选择模型": {"en-US": "Select Model", "de-DE": "<PERSON><PERSON> auswählen", "$files": ["src/pages/PromptWords/page.tsx(3292-3300)"], "$id": 1793}, "你是一个智能、可靠、礼貌且高效的 AI 助手，请你根据用户的要求提供清晰、有条理、准确的回答。": {"en-US": "You are an intelligent, reliable, polite, and efficient AI assistant. Please provide clear, organized, and accurate answers according to user requirements.", "de-DE": "<PERSON>e sind ein intelligenter, zu<PERSON><PERSON><PERSON><PERSON><PERSON>, hö<PERSON><PERSON>r und effizienter KI-Assistent, der Sie bittet, klare, systematische und präzise Antworten auf die Anforderungen der Benutzer zu geben.", "$files": ["src/pages/PromptWords/page.tsx(4808-4870)"], "$id": 1794}, "知识库": {"en-US": "Knowledge Base", "de-DE": "Wissensdatenbank", "$files": ["src/pages/PromptWords/page.tsx(7267-7274)"], "$id": 958}, "创建会话失败": {"en-US": "Session creation failed", "de-DE": "Erstellung der Sitzung fehlgeschlagen", "$files": ["src/pages/PromptWords/page.tsx(9205-9215)"], "$id": 1795}, "创建会话失败，使用备用方案:": {"en-US": "Failed to create session, use alternative solution:", "de-DE": "Das Erstellen einer Sitzung ist fehlgeschlagen. Verwenden Sie eine Alternative:", "$files": ["src/pages/PromptWords/page.tsx(9274-9292)"], "$id": 1796}, "会话创建失败，使用本地生成的会话ID": {"en-US": "Session creation failed, using locally generated session ID", "de-DE": "Die Sitzungserstellung ist fehlgeschlagen, mit lokal generierter Sitzungsid", "$files": ["src/pages/PromptWords/page.tsx(9325-9347)"], "$id": 1797}, "本地URL无效:": {"en-US": "Invalid local URL:", "de-DE": "Ungültige lokale URL:", "$files": ["src/pages/PromptWords/page.tsx(9847-9859)"], "$id": 1798}, "图片没有有效的URL:": {"en-US": "The image does not have a valid URL:", "de-DE": "Das Bild hat keine gültige URL:", "$files": ["src/pages/PromptWords/page.tsx(9999-10014)"], "$id": 1799}, "删除会话失败:": {"en-US": "Failed to delete session:", "de-DE": "Das Löschen der Sitzung ist fehlgeschlagen:", "$files": ["src/pages/PromptWords/page.tsx(15333-15344)"], "$id": 1800}, "删除会话失败，但将继续清除本地数据": {"en-US": "Delete session failed, but will continue to clear local data", "de-DE": "Das Löschen der Sitzung ist fehlgeschlagen, aber die lokalen Daten werden weiterhin gelöscht", "$files": ["src/pages/PromptWords/page.tsx(15405-15426)"], "$id": 1801}, "添加模型": {"en-US": "Add Model", "de-DE": "<PERSON><PERSON>", "$files": ["src/pages/PromptWords/page.tsx(17787-17795)", "src/pages/ModelSetting/pages/ModelBaseList/page.tsx(6065-6073)", "src/pages/ModelSetting/pages/ModelBaseList/page.tsx(6919-6927)", "src/pages/ModelSetting/pages/ThirdPartyModel/page.tsx(8224-8232)", "src/pages/ModelSetting/pages/ThirdPartyModel/components/ConfigModel.tsx(2843-2851)"], "$id": 272}, "取消对比": {"en-US": "Cancel comparison", "de-DE": "Vergleich aufheben", "$files": ["src/pages/PromptWords/page.tsx(18475-18483)"], "$id": 1802}, "同步参数": {"en-US": "synchronization parameters", "de-DE": "Synchronisierungsparameter", "$files": ["src/pages/PromptWords/page.tsx(19053-19061)"], "$id": 1803}, "清除对话": {"en-US": "Clear Conversation", "de-DE": "Dialog <PERSON>", "$files": ["src/pages/PromptWords/page.tsx(21542-21550)"], "$id": 961}, "预览": {"en-US": "Preview", "de-DE": "Vorschau", "$files": ["src/pages/PromptWords/page.tsx(22394-22400)"], "$id": 1804}, "上传中...": {"en-US": "Uploading ..", "de-DE": "Hochgeladen. ..", "$files": ["src/pages/PromptWords/page.tsx(24233-24243)"], "$id": 1805}, "上传图片 (JPG/PNG)": {"en-US": "Upload images (JPG/PNG)", "de-DE": "<PERSON><PERSON><PERSON> (JPG/PNG)", "$files": ["src/pages/PromptWords/page.tsx(24247-24265)"], "$id": 1806}, "请确保两个模型都已选择...": {"en-US": "Please ensure that both models have been selected ..", "de-DE": "<PERSON>te stellen Si<PERSON> sicher, dass beide Modelle ausgewählt sind. ..", "$files": ["src/pages/PromptWords/page.tsx(26048-26066)"], "$id": 1807}, "请先选择模型...": {"en-US": "Please select the model first ..", "de-DE": "Bitte wählen Sie zuerst das Modell aus. ..", "$files": ["src/pages/PromptWords/page.tsx(26088-26101)"], "$id": 1808}, "内容由AI生成，无法确保真实准确，仅供参考，并请遵守本平台": {"en-US": "The content is generated by AI and cannot be guaranteed to be true and accurate. It is for reference only and please comply with this platform", "de-DE": "<PERSON><PERSON><PERSON>, die von KI generiert wurden, können nicht sichergestellt werden, dass sie wahr und korrekt sind, dienen nur zur Information und halten sich bitte an diese Plattform.", "$files": ["src/pages/PromptWords/page.tsx(26273-26306)"], "$id": 1809}, "《用户协议》": {"en-US": "User Agreement", "de-DE": "Nutzervereinbarung", "$files": ["src/pages/PromptWords/page.tsx(26442-26452)"], "$id": 1810}, "及国家网络信息安全相关规定": {"en-US": "And relevant regulations on national network information security", "de-DE": "Nationale Vorschriften zur Informationssicherheit im Netzwerk", "$files": ["src/pages/PromptWords/page.tsx(26485-26502)"], "$id": 1811}, "获取计划模板失败": {"en-US": "Failed to obtain the plan template", "de-DE": "Fehler beim Abrufen der Planvorlage", "$files": ["src/pages/RunningPage/index.tsx(1198-1210)", "src/pages/RunningPage/index.tsx(1721-1733)", "src/pages/RunningPage/index.tsx(2323-2335)"], "$id": 967}, "错误:": {"en-US": "Error:", "de-DE": "<PERSON><PERSON>:", "$files": ["src/pages/RunningPage/index.tsx(1474-1481)", "src/pages/RunningPage/index.tsx(1921-1928)", "src/pages/RunningPage/index.tsx(2523-2530)", "src/pages/Setting/pages/TrainMethod/page.tsx(729-736)"], "$id": 968}, "返回": {"en-US": "Back", "de-DE": "Zurück", "$files": ["src/pages/RunningPage/index.tsx(3427-3433)", "src/pages/TaskDetail/index.tsx(1212-1218)", "src/pages/UserPage/index.tsx(2946-2952)", "src/pages/Datasets/children/DatasetsDetails/index.tsx(1883-1889)", "src/pages/KnowledgeHub/children/CreateKnowledge/page.tsx(1877-1883)", "src/pages/KnowledgeHub/children/KnowledgeDetails/page.tsx(6809-6815)", "src/pages/KnowledgeHub/children/UploadKnowledgeFile/page.tsx(1527-1533)", "src/pages/ModelEvaluation/children/ModelEvaluationResult/page.tsx(997-1003)", "src/pages/ModelEvaluation/children/RunModelEvaluation/page.tsx(9265-9271)", "src/pages/ModelSetting/pages/SavedModel/pages/log/page.tsx(2259-2265)", "src/pages/ModelSetting/pages/TrainedModel/pages/log/page.tsx(2279-2285)"], "$id": 113}, "运行AI任务": {"en-US": "Run AI task", "de-DE": "AI-Aufgabe ausführen", "$files": ["src/pages/RunningPage/index.tsx(3466-3476)"], "$id": 969}, "连接器": {"en-US": "Connector", "de-DE": "Konnektor", "$files": ["src/pages/TaskDetail/index.tsx(425-432)"], "$id": 970}, "数据": {"en-US": "Data", "de-DE": "Daten", "$files": ["src/pages/TaskDetail/index.tsx(486-492)"], "$id": 971}, "基础模型": {"en-US": "Base Model", "de-DE": "Basis-Modell", "$files": ["src/pages/TaskDetail/index.tsx(541-549)", "src/pages/ModelSetting/pages/SavedModel/page.tsx(4687-4695)", "src/pages/ModelSetting/pages/TrainedModel/page.tsx(1818-1826)", "src/pages/ModelSetting/pages/DeployedModel/components/ExpandContent.tsx(2155-2163)", "src/pages/ModelSetting/pages/SavedModel/components/ExpandContent.tsx(2183-2191)", "src/pages/ModelSetting/pages/ModelBaseList/components/ExpandContent.tsx(898-906)", "src/pages/ModelSetting/pages/TrainedModel/components/ExpandContent.tsx(1180-1188)"], "$id": 275}, "训练资源": {"en-US": "Training resources", "de-DE": "Trainingsressourcen", "$files": ["src/pages/TaskDetail/index.tsx(603-611)"], "$id": 972}, "训练参数": {"en-US": "Training Parameters", "de-DE": "Trainingsparameter", "$files": ["src/pages/TaskDetail/index.tsx(664-672)"], "$id": 106}, "任务详情": {"en-US": "Task details", "de-DE": "Aufgabendetails", "$files": ["src/pages/TaskDetail/index.tsx(1424-1432)", "src/pages/TaskDetail/index.tsx(1830-1838)"], "$id": 973}, "保存新模型": {"en-US": "Save new model", "de-DE": "Neues Modell speichern", "$files": ["src/pages/TaskDetail/index.tsx(2851-2860)"], "$id": 974}, "复制任务": {"en-US": "Copy task", "de-DE": "Aufgabe kopieren", "$files": ["src/pages/TaskDetail/index.tsx(3201-3209)"], "$id": 975}, "训练新版本": {"en-US": "Train new version", "de-DE": "Neue Version trainieren", "$files": ["src/pages/TaskDetail/index.tsx(3255-3264)"], "$id": 976}, "请输入正确的个人信息代码": {"en-US": "Please enter the correct personal information code", "de-DE": "Bitte geben Si<PERSON> den richtigen persönlichen Informationscode ein", "$files": ["src/pages/UserPage/ShowButtonModal.tsx(845-861)"], "$id": 988}, "请输入个人信息代码:": {"en-US": "Please enter your personal information code:", "de-DE": "Bitte geben Sie den persönlichen Informationscode ein:", "$files": ["src/pages/UserPage/ShowButtonModal.tsx(1107-1121)"], "$id": 989}, "关闭ai助手": {"en-US": "Turn off AI assistant", "de-DE": "AI-<PERSON><PERSON><PERSON>", "$files": ["src/pages/UserPage/ShowButtonModal.tsx(1394-1404)"], "$id": 990}, "开启ai助手": {"en-US": "Turn on AI assistant", "de-DE": "AI-Assistent aktivieren", "$files": ["src/pages/UserPage/ShowButtonModal.tsx(1408-1418)"], "$id": 991}, "修改成功": {"en-US": "Modification successful", "de-DE": "Änderung erfolgreich", "$files": ["src/pages/UserPage/UserSettingModal.tsx(706-714)"], "$id": 992}, "请输入密码": {"en-US": "Please enter password", "de-DE": "Bitte Passwort eingeben", "$files": ["src/pages/UserPage/UserSettingModal.tsx(1092-1101)", "src/pages/Setting/pages/UserManagement/page.tsx(5322-5331)"], "$id": 337}, "请输入新密码": {"en-US": "Please enter a new password", "de-DE": "Bitte geben Sie das neue Passwort ein", "$files": ["src/pages/UserPage/UserSettingModal.tsx(1253-1263)"], "$id": 994}, "请再次输入新密码": {"en-US": "Please enter the new password again", "de-DE": "Bitte geben Sie das neue Passwort erneut ein", "$files": ["src/pages/UserPage/UserSettingModal.tsx(1434-1446)"], "$id": 995}, "提交": {"en-US": "Submit", "de-DE": "<PERSON><PERSON><PERSON><PERSON>", "$files": ["src/pages/UserPage/UserSettingModal.tsx(2075-2081)", "src/pages/Datasets/children/UploadDatasets/index.tsx(13589-13595)", "src/pages/Setting/pages/DeployEnv/page.tsx(5747-5753)"], "$id": 996}, "上传失败": {"en-US": "Upload failed", "de-DE": "Upload fehlgeschlagen", "$files": ["src/pages/UserPage/index.tsx(1782-1790)"], "$id": 997}, "错误：": {"en-US": "Error:", "de-DE": "<PERSON><PERSON>:", "$files": ["src/pages/UserPage/index.tsx(1856-1863)"], "$id": 998}, "设置": {"en-US": "Settings", "de-DE": "Einstellungen", "$files": ["src/pages/UserPage/index.tsx(3042-3048)"], "$id": 999}, "头像：": {"en-US": "Avatar:", "de-DE": "Avatar:", "$files": ["src/pages/UserPage/index.tsx(3203-3210)"], "$id": 1000}, "上传": {"en-US": "Upload", "de-DE": "Hochladen", "$files": ["src/pages/UserPage/index.tsx(3414-3420)"], "$id": 1001}, "姓名：": {"en-US": "Name:", "de-DE": "Name:", "$files": ["src/pages/UserPage/index.tsx(3639-3646)"], "$id": 1002}, "邮箱：": {"en-US": "Email:", "de-DE": "E-Mail:", "$files": ["src/pages/UserPage/index.tsx(3737-3744)"], "$id": 1003}, "公司：": {"en-US": "Company:", "de-DE": "Firma:", "$files": ["src/pages/UserPage/index.tsx(3832-3839)"], "$id": 1004}, "计划范围：": {"en-US": "Plan scope:", "de-DE": "Planumfang:", "$files": ["src/pages/UserPage/index.tsx(3928-3937)"], "$id": 1005}, "AI助手设置": {"en-US": "AI assistant settings", "de-DE": "AI-Assistent-Einstellungen", "$files": ["src/pages/UserPage/index.tsx(4297-4307)"], "$id": 1006}, "当前密码不能为空": {"en-US": "The current password cannot be empty", "de-DE": "Das aktuelle Passwort darf nicht leer sein", "$files": ["src/components/Layout/components/ChangePasswordModal.tsx(1421-1433)"], "$id": 1532}, "新密码不能为空": {"en-US": "The new password cannot be empty", "de-DE": "Das neue Passwort darf nicht leer sein", "$files": ["src/components/Layout/components/ChangePasswordModal.tsx(1527-1538)"], "$id": 1533}, "新密码长度至少为8位": {"en-US": "The new password must be at least 8 characters long", "de-DE": "Das neue Passwort muss mindestens 8 Zeichen lang sein", "$files": ["src/components/Layout/components/ChangePasswordModal.tsx(1642-1656)"], "$id": 1534}, "两次输入的密码不一致": {"en-US": "The passwords entered twice are inconsistent", "de-DE": "Die eingegebenen Passwörter stimmen nicht überein", "$files": ["src/components/Layout/components/ChangePasswordModal.tsx(1782-1796)"], "$id": 1535}, "密码修改失败": {"en-US": "Password change failed", "de-DE": "Passwortänderung fehlgeschlagen", "$files": ["src/components/Layout/components/ChangePasswordModal.tsx(2229-2239)", "src/components/Layout/components/ChangePasswordModal.tsx(2625-2635)"], "$id": 1536}, "密码修改成功": {"en-US": "Password change successful", "de-DE": "Passwortänderung erfolgreich", "$files": ["src/components/Layout/components/ChangePasswordModal.tsx(2373-2383)"], "$id": 1537}, "您的密码已成功更新": {"en-US": "Your password has been successfully updated", "de-DE": "Ihr Passwort wurde erfolgreich aktualisiert", "$files": ["src/components/Layout/components/ChangePasswordModal.tsx(2407-2420)"], "$id": 1538}, "请检查您的当前密码是否正确": {"en-US": "Please check if your current password is correct", "de-DE": "Bitte überprüfen Sie, ob Ihr aktuelles Passwort korrekt ist", "$files": ["src/components/Layout/components/ChangePasswordModal.tsx(2659-2676)"], "$id": 1539}, "请输入您的当前密码和新密码": {"en-US": "Please enter your current password and new password", "de-DE": "Bitte geben Sie Ihr aktuelles Passwort und Ihr neues Passwort ein", "$files": ["src/components/Layout/components/ChangePasswordModal.tsx(3064-3081)"], "$id": 1540}, "当前密码": {"en-US": "Current password", "de-DE": "Aktuelles Passwort", "$files": ["src/components/Layout/components/ChangePasswordModal.tsx(3290-3298)"], "$id": 1541}, "新密码": {"en-US": "New password", "de-DE": "Neues Passwort", "$files": ["src/components/Layout/components/ChangePasswordModal.tsx(3836-3843)"], "$id": 1542}, "密码长度至少为8位": {"en-US": "Password must be at least 8 characters long", "de-DE": "Das Passwort muss mindestens 8 Zeichen lang sein", "$files": ["src/components/Layout/components/ChangePasswordModal.tsx(4332-4345)"], "$id": 1543}, "确认新密码": {"en-US": "Confirm new password", "de-DE": "Neues Passwort bestätigen", "$files": ["src/components/Layout/components/ChangePasswordModal.tsx(4467-4476)"], "$id": 1544}, "提交中...": {"en-US": "Submitting...", "de-DE": "Wird gesendet...", "$files": ["src/components/Layout/components/ChangePasswordModal.tsx(5271-5281)"], "$id": 1545}, "确认修改": {"en-US": "Confirm changes", "de-DE": "Änderung bestätigen", "$files": ["src/components/Layout/components/ChangePasswordModal.tsx(5285-5293)"], "$id": 1546}, "总量": {"en-US": "Total Volume", "de-DE": "Gesamtmenge", "$files": ["src/pages/Home/components/bar-chart-section.tsx(1289-1295)"], "$id": 1592}, "微调任务": {"en-US": "Fine-tuning Tasks", "de-DE": "Feinabstimmungsaufgaben", "$files": ["src/pages/Home/components/bar-chart-section.tsx(5258-5266)"], "$id": 1593}, "查看更多": {"en-US": "View More", "de-DE": "<PERSON><PERSON> anzeigen", "$files": ["src/pages/Home/components/bar-chart-section.tsx(5452-5460)", "src/pages/Home/components/model-evaluation-chart.tsx(2434-2442)", "src/pages/Home/components/pie-chart-section.tsx(2827-2835)", "src/pages/Home/components/third-modle-chart.tsx(6222-6230)"], "$id": 1594}, "模型评估分布": {"en-US": "Model Evaluation Distribution", "de-DE": "Modellbewertungsverteilung", "$files": ["src/pages/Home/components/model-evaluation-chart.tsx(2263-2273)"], "$id": 1595}, "模型占比": {"en-US": "Model Percentage", "de-DE": "Modellanteil", "$files": ["src/pages/Home/components/pie-chart-section.tsx(1748-1756)"], "$id": 1596}, "基础模型占比": {"en-US": "Base Model Percentage", "de-DE": "Grundmodellanteil", "$files": ["src/pages/Home/components/pie-chart-section.tsx(2649-2659)"], "$id": 1597}, "第三方模型分布": {"en-US": "Third-party Model Distribution", "de-DE": "Drittanbieter-Modellverteilung", "$files": ["src/pages/Home/components/third-modle-chart.tsx(6037-6048)"], "$id": 1598}, "列设置": {"en-US": "Column settings", "de-DE": "Spaltenkonfiguration", "$files": ["src/pages/ModelEvaluation/components/ConfigTable.tsx(1660-1667)"], "$id": 1065}, "复制成功": {"en-US": "<PERSON><PERSON> succeeded", "de-DE": "<PERSON><PERSON><PERSON>", "$files": ["src/pages/PromptWords/components/ChartView.tsx(2909-2917)"], "$id": 1007}, "复制失败": {"en-US": "Co<PERSON> failed", "de-DE": "Ko<PERSON>ren fehlgeschlagen", "$files": ["src/pages/PromptWords/components/ChartView.tsx(2979-2987)"], "$id": 1008}, "服务器发生错误，请稍后重试": {"en-US": "An error occurred on the server, please try again later", "de-DE": "<PERSON>f dem <PERSON> ist ein Fehler aufgetreten, bitte versuchen Sie es später erneut", "$files": ["src/pages/PromptWords/components/ChartView.tsx(6645-6662)"], "$id": 1009}, "正常": {"en-US": "Normal", "de-DE": "Normal", "$files": ["src/pages/Datasets/children/DatasetsDetails/index.tsx(490-496)"], "$id": 1516}, "异常": {"en-US": "Abnormal", "de-DE": "Abnormal", "$files": ["src/pages/Datasets/children/DatasetsDetails/index.tsx(506-512)"], "$id": 1517}, "获取数据集详情失败：": {"en-US": "Failed to Retrieve Dataset Details:", "de-DE": "Fehler beim Abrufen der Datensatzdetails:", "$files": ["src/pages/Datasets/children/DatasetsDetails/index.tsx(1418-1432)", "src/pages/Datasets/children/DatasetsDetails/components/DataPart.tsx(1586-1600)"], "$id": 1109}, "数据集详情": {"en-US": "Dataset details", "de-DE": "Datensatzdetails", "$files": ["src/pages/Datasets/children/DatasetsDetails/index.tsx(1955-1964)"], "$id": 1519}, "数据集描述": {"en-US": "Dataset Description", "de-DE": "Datenbankbeschreibung", "$files": ["src/pages/Datasets/children/DatasetsDetails/index.tsx(2158-2167)", "src/pages/Datasets/children/UploadDatasets/index.tsx(12333-12342)", "src/pages/Datasets/children/UploadDatasets/index.tsx(14212-14221)"], "$id": 1017}, "总数": {"en-US": "Total", "de-DE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "$files": ["src/pages/Datasets/children/DatasetsDetails/index.tsx(2272-2278)"], "$id": 1520}, "数据列表": {"en-US": "Data list", "de-DE": "<PERSON><PERSON><PERSON><PERSON>", "$files": ["src/pages/Datasets/children/DatasetsDetails/index.tsx(2997-3005)"], "$id": 1521}, "选择的文件夹中未找到 .jsonl 文件": {"en-US": "No .jsonl file found in the selected folder", "de-DE": "In dem ausgewählten Ordner wurden keine .jsonl-Dateien gefunden", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(3026-3050)"], "$id": 1735}, "文件上传成功": {"en-US": "File uploaded successfully", "de-DE": "Dateiupload erfolgreich", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(7113-7123)"], "$id": 1736}, "文件上传失败": {"en-US": "File upload failed", "de-DE": "Dateiupload fehlgeschlagen", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(7290-7300)"], "$id": 1737}, "多模态数据集上传成功": {"en-US": "Multimodal dataset uploaded successfully", "de-DE": "Multimodaler Datensatz erfolgreich hochgeladen", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(7501-7515)"], "$id": 1738}, "请选择要上传的文件": {"en-US": "Please select the file to upload", "de-DE": "Bitte wählen Sie die hochzuladende Datei aus", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(9843-9856)", "src/pages/Datasets/children/UploadDatasets/index.tsx(12696-12709)", "src/pages/KnowledgeHub/children/UploadKnowledgeFile/page.tsx(1864-1877)"], "$id": 1020}, "数据集上传成功": {"en-US": "Dataset uploaded successfully", "de-DE": "Datensatz erfolgreich hochgeladen", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(10261-10272)"], "$id": 1010}, "上传数据集时发生错误:": {"en-US": "An error occurred while uploading the dataset:", "de-DE": "Beim Ho<PERSON>laden des Datensatzes ist ein Fehler aufgetreten:", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(10439-10454)"], "$id": 1012}, "上传数据集时发生错误": {"en-US": "An error occurred while uploading the dataset", "de-DE": "<PERSON>im Ho<PERSON>laden des Datensatzes ist ein Fehler aufgetreten", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(10487-10501)"], "$id": 1013}, "上传组件未初始化": {"en-US": "Upload component not initialized", "de-DE": "Upload-Komponente nicht initialisiert", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(10627-10639)"], "$id": 1739}, "请选择要上传的文件夹": {"en-US": "Please select a folder to upload", "de-DE": "Bitte wählen Sie den zu uploadenden Ordner aus", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(10740-10754)"], "$id": 1740}, "请先选择一个JSONL文件作为展示文件": {"en-US": "Please select a JSONL file as the display file first", "de-DE": "Bitte wählen Sie zunächst eine JSONL-Datei als Anzeigedatei aus", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(10833-10856)"], "$id": 1741}, "上传失败，请重试": {"en-US": "Upload failed, please try again", "de-DE": "Upload feh<PERSON><PERSON><PERSON>n, bitte erneut versuchen", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(11291-11303)", "src/pages/KnowledgeHub/children/UploadKnowledgeFile/page.tsx(1183-1195)"], "$id": 1049}, "返回数据集列表": {"en-US": "Return to dataset list", "de-DE": "Zurück zur Datensatzliste", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(11670-11681)"], "$id": 1742}, "上传新数据集": {"en-US": "Upload new dataset", "de-DE": "Neuen Datensatz hochladen", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(11791-11801)"], "$id": 1743}, "单文件上传": {"en-US": "Single file upload", "de-DE": "Einzelner Dateiupload", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(11902-11911)"], "$id": 1744}, "请输入数据集名称": {"en-US": "Please enter the dataset name", "de-DE": "Bitte geben Sie den Namen der Datenbank ein", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(12205-12217)", "src/pages/Datasets/children/UploadDatasets/index.tsx(14084-14096)"], "$id": 1016}, "请输入数据集描述": {"en-US": "Please enter the dataset description", "de-DE": "Bitte geben Sie die Beschreibung der Datenbank ein", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(12435-12447)", "src/pages/Datasets/children/UploadDatasets/index.tsx(14314-14326)"], "$id": 1018}, "上传文件": {"en-US": "Upload File", "de-DE": "<PERSON><PERSON> ho<PERSON>n", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(12581-12589)"], "$id": 1019}, "点击或拖拽文件到此区域上传": {"en-US": "Click or drag files to this area to upload", "de-DE": "<PERSON>licken Sie oder ziehen Sie die Datei in diesen Bereich, um sie hochzuladen", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(13095-13112)", "src/pages/KnowledgeHub/children/UploadKnowledgeFile/page.tsx(2380-2397)"], "$id": 1051}, "支持单文件上传，文件大小不超过 100MB，支持 .csv, .json, .txt 等格式": {"en-US": "Supports single file upload, file size not exceeding 100MB, supports formats such as .csv, .json, .txt", "de-DE": "Unterstützt den Upload einzel<PERSON>, die Dateigröße darf 100 MB nicht überschreiten, unterstützt Formate wie .csv, .json, .txt etc.", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(13215-13290)"], "$id": 1745}, "多模态数据集上传": {"en-US": "Multimodal dataset upload", "de-DE": "Multimodaler Datensatzupload", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(13778-13790)"], "$id": 1746}, "上传文件夹": {"en-US": "Upload folder", "de-DE": "Ordner hochladen", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(14442-14451)", "src/pages/Datasets/children/UploadDatasets/index.tsx(19979-19988)"], "$id": 1747}, "选择多模态数据集文件夹": {"en-US": "Select a multimodal dataset folder", "de-DE": "<PERSON><PERSON><PERSON><PERSON> Sie den Ordner mit dem multimodalen Datensatz aus", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(14776-14791)"], "$id": 1748}, "点击下方按钮选择包含多模态数据的完整文件夹": {"en-US": "Click the button below to select the entire folder containing multimodal data", "de-DE": "<PERSON>lick<PERSON> Si<PERSON> auf die Schaltfläche unten, um den vollständigen Ordner mit multimodalen Daten auszuwählen", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(14911-14936)"], "$id": 1749}, "💡 提示：请确保文件夹中包含 .jsonl 文件": {"en-US": "💡 Tip: Ensure that the folder contains a .jsonl file", "de-DE": "💡 Hinweis: <PERSON><PERSON><PERSON>, dass der Ordner eine .jsonl-<PERSON><PERSON> enthält", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(15056-15085)"], "$id": 1750}, "选择文件夹": {"en-US": "Select folder", "de-DE": "Ordner auswählen", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(15844-15853)"], "$id": 1751}, "已选择文件夹，共 {count} 个文件": {"en-US": "Folder selected, with a total of {count} files", "de-DE": "Ausgewählter Ordner, insgesamt {count} <PERSON><PERSON>", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(16078-16102)"], "$id": 1752}, "取消上传": {"en-US": "Cancel upload", "de-DE": "Hochladen abbrechen", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(16689-16697)"], "$id": 1753}, "已选择的文件夹结构": {"en-US": "Structure of the selected folder", "de-DE": "Struktur des ausgewählten Ordners", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(17199-17212)"], "$id": 1754}, "如需重新选择，请点击上方\"取消上传\"按钮": {"en-US": "To reselect, click the \"Cancel upload\" button above", "de-DE": "Um neu auszuw<PERSON><PERSON>en, klicken Sie auf die Schaltfläche \"Hochladen abbrechen\" oben", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(17337-17361)"], "$id": 1755}, "选择展示的 JSONL 文件": {"en-US": "Select the JSONL file for display", "de-DE": "JSONL-Datei für die Anzeige auswählen", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(17923-17941)"], "$id": 1756}, "在上传的文件夹中找到以下 JSONL 文件，请选择一个作为数据集的展示文件：": {"en-US": "Find the following JSONL files in the uploaded folder and select one as the dataset's display file:", "de-DE": "In dem hochgeladenen Ordner wurden die folgenden JSONL-Dateien gefunden. Bitte wählen Sie eine als Anzeigedatei für den Datensatz aus:", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(18137-18208)"], "$id": 1757}, "确认选择并上传数据集": {"en-US": "Confirm selection and upload dataset", "de-DE": "Bestätigen Sie die Auswahl und laden Sie den Datensatz hoch", "$files": ["src/pages/Datasets/children/UploadDatasets/index.tsx(19937-19951)"], "$id": 1758}, "获取任务": {"en-US": "Get Task", "de-DE": "Abrufen der Aufgabe", "$files": ["src/pages/FineTuningLog/children/CompareLogs/index.tsx(1871-1879)", "src/pages/FineTuningLog/children/CompareLogs/index.tsx(1993-2001)", "src/pages/FineTuningLog/children/CompareLogs/index.tsx(3401-3409)"], "$id": 1490}, "的图表数据失败": {"en-US": "The chart data failed", "de-DE": "Fehler beim Abrufen der Diagrammdaten", "$files": ["src/pages/FineTuningLog/children/CompareLogs/index.tsx(1896-1907)", "src/pages/FineTuningLog/children/CompareLogs/index.tsx(2018-2029)"], "$id": 1491}, "的参数失败": {"en-US": "Parameter failure", "de-DE": "Fehler bei den Parametern", "$files": ["src/pages/FineTuningLog/children/CompareLogs/index.tsx(3426-3435)"], "$id": 1492}, "获取参数数据失败:": {"en-US": "Failed to obtain parameter data:", "de-DE": "Fehler beim Abrufen der Parameterdaten:", "$files": ["src/pages/FineTuningLog/children/CompareLogs/index.tsx(4317-4330)"], "$id": 1493}, "返回日志": {"en-US": "Return to Log", "de-DE": "Zurück zum Protokoll", "$files": ["src/pages/FineTuningLog/children/CompareLogs/index.tsx(4703-4711)", "src/pages/FineTuningLog/children/LogDetails/index.tsx(974-982)"], "$id": 1025}, "超参数对比": {"en-US": "Hyperparameter comparison", "de-DE": "Hyperparametervergleich", "$files": ["src/pages/FineTuningLog/children/CompareLogs/index.tsx(4989-4998)"], "$id": 1494}, "任务": {"en-US": "Task", "de-DE": "Aufgabe", "$files": ["src/pages/FineTuningLog/children/CompareLogs/index.tsx(6756-6762)", "src/pages/FineTuningLog/children/CompareLogs/index.tsx(8468-8474)", "src/pages/ModelSetting/pages/DeployedModel/components/ExpandContent.tsx(1028-1034)", "src/pages/ModelSetting/pages/SavedModel/components/ExpandContent.tsx(1338-1344)", "src/pages/ModelSetting/pages/ModelBaseList/components/ExpandContent.tsx(627-633)", "src/pages/ModelSetting/pages/TrainedModel/components/ExpandContent.tsx(537-543)"], "$id": 294}, "数值": {"en-US": "Value", "de-DE": "Wert", "$files": ["src/pages/FineTuningLog/children/CompareLogs/index.tsx(7127-7133)", "src/pages/FineTuningLog/children/CompareLogs/components/TaskDetails.tsx(1869-1875)", "src/pages/FineTuningLog/children/LogDetails/components/TaskEvaluation.tsx(1905-1911)"], "$id": 1115}, "步数": {"en-US": "Steps", "de-DE": "<PERSON><PERSON><PERSON>", "$files": ["src/pages/FineTuningLog/children/CompareLogs/index.tsx(7168-7174)", "src/pages/FineTuningLog/children/CompareLogs/components/TaskDetails.tsx(2038-2044)", "src/pages/FineTuningLog/children/LogDetails/components/TaskEvaluation.tsx(2034-2040)"], "$id": 1116}, "参数名称": {"en-US": "Parameter", "de-DE": "Parametername", "$files": ["src/pages/FineTuningLog/children/CompareLogs/index.tsx(8189-8197)"], "$id": 1495}, "任务概览": {"en-US": "Task Overview", "de-DE": "Aufgabenübersicht", "$files": ["src/pages/FineTuningLog/children/LogDetails/index.tsx(372-380)"], "$id": 1022}, "任务评估": {"en-US": "Task Evaluation", "de-DE": "Aufgabenbewertung", "$files": ["src/pages/FineTuningLog/children/LogDetails/index.tsx(466-474)"], "$id": 1023}, "任务日志": {"en-US": "Task Log", "de-DE": "Aufgabenprotokoll", "$files": ["src/pages/FineTuningLog/children/LogDetails/index.tsx(564-572)"], "$id": 1024}, "微调日志详情": {"en-US": "Fine-tuning Log Details", "de-DE": "Feinabstimmungs-Protokolldetails", "$files": ["src/pages/FineTuningLog/children/LogDetails/index.tsx(1209-1219)"], "$id": 1026}, "创建成功": {"en-US": "Creation Successful", "de-DE": "Erfolgreich erstellt", "$files": ["src/pages/KnowledgeHub/children/CreateKnowledge/page.tsx(724-732)"], "$id": 1027}, "创建失败，请重试": {"en-US": "Creation failed, please try again", "de-DE": "Erstellung fehlgeschlagen, bitte erneut versuchen", "$files": ["src/pages/KnowledgeHub/children/CreateKnowledge/page.tsx(819-831)"], "$id": 1028}, "创建失败": {"en-US": "Creation Failed", "de-DE": "Erstellung fehlgeschlagen", "$files": ["src/pages/KnowledgeHub/children/CreateKnowledge/page.tsx(885-893)"], "$id": 1029}, "请输入知识库名称": {"en-US": "Please enter the knowledge base name", "de-DE": "Bitte geben Sie den Namen der Wissensdatenbank ein", "$files": ["src/pages/KnowledgeHub/children/CreateKnowledge/page.tsx(2219-2231)", "src/pages/KnowledgeHub/children/CreateKnowledge/page.tsx(2303-2315)"], "$id": 1030}, "请输入知识库描述": {"en-US": "Please enter the knowledge base description", "de-DE": "Bitte geben Sie die Beschreibung der Wissensdatenbank ein", "$files": ["src/pages/KnowledgeHub/children/CreateKnowledge/page.tsx(2472-2484)", "src/pages/KnowledgeHub/children/CreateKnowledge/page.tsx(2565-2577)"], "$id": 1031}, "请选择向量模型": {"en-US": "Please select the vector model", "de-DE": "Bitte wählen Sie das Vektormodell aus", "$files": ["src/pages/KnowledgeHub/children/CreateKnowledge/page.tsx(2747-2758)", "src/pages/KnowledgeHub/children/CreateKnowledge/page.tsx(2845-2856)"], "$id": 1032}, "保存": {"en-US": "Save", "de-DE": "Speichern", "$files": ["src/pages/KnowledgeHub/children/CreateKnowledge/page.tsx(3119-3125)", "src/pages/KnowledgeHub/children/UploadKnowledgeFile/page.tsx(2975-2981)", "src/pages/ModelSetting/pages/TrainedModel/page.tsx(3007-3013)"], "$id": 281}, "文件解析失败：": {"en-US": "File parsing failed:", "de-DE": "<PERSON><PERSON> be<PERSON>:", "$files": ["src/pages/KnowledgeHub/children/KnowledgeDetails/page.tsx(3298-3309)"], "$id": 1034}, "获取文件列表失败": {"en-US": "Failed to get file list", "de-DE": "Fehler beim Abrufen der Dateiliste", "$files": ["src/pages/KnowledgeHub/children/KnowledgeDetails/page.tsx(3921-3933)"], "$id": 1035}, "未解析": {"en-US": "Not parsed", "de-DE": "<PERSON>cht geparst", "$files": ["src/pages/KnowledgeHub/children/KnowledgeDetails/page.tsx(4489-4496)"], "$id": 1036}, "解析成功": {"en-US": "Parsed successfully", "de-DE": "Erfolgreich geparst", "$files": ["src/pages/KnowledgeHub/children/KnowledgeDetails/page.tsx(4533-4541)"], "$id": 1037}, "解析失败": {"en-US": "Parsing failed", "de-DE": "<PERSON><PERSON> be<PERSON>", "$files": ["src/pages/KnowledgeHub/children/KnowledgeDetails/page.tsx(4581-4589)"], "$id": 1038}, "解析中": {"en-US": "Parsing in progress", "de-DE": "<PERSON>ir<PERSON> g<PERSON><PERSON>t", "$files": ["src/pages/KnowledgeHub/children/KnowledgeDetails/page.tsx(4629-4636)"], "$id": 1039}, "文件名称": {"en-US": "File Name", "de-DE": "Dateiname", "$files": ["src/pages/KnowledgeHub/children/KnowledgeDetails/page.tsx(5131-5139)"], "$id": 1040}, "文件大小": {"en-US": "File Size", "de-DE": "Dateigröße", "$files": ["src/pages/KnowledgeHub/children/KnowledgeDetails/page.tsx(5220-5228)"], "$id": 1041}, "文件格式": {"en-US": "File Format", "de-DE": "Dateiformat", "$files": ["src/pages/KnowledgeHub/children/KnowledgeDetails/page.tsx(5309-5317)"], "$id": 1042}, "导入时间": {"en-US": "Import Time", "de-DE": "Importzeit", "$files": ["src/pages/KnowledgeHub/children/KnowledgeDetails/page.tsx(5625-5633)"], "$id": 1043}, "下载": {"en-US": "Download", "de-DE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "$files": ["src/pages/KnowledgeHub/children/KnowledgeDetails/page.tsx(6044-6050)"], "$id": 1044}, "搜索文件": {"en-US": "Search files", "de-DE": "<PERSON><PERSON> suchen", "$files": ["src/pages/KnowledgeHub/children/KnowledgeDetails/page.tsx(6201-6209)"], "$id": 1045}, "导入文件": {"en-US": "Import files", "de-DE": "Datei importieren", "$files": ["src/pages/KnowledgeHub/children/KnowledgeDetails/page.tsx(6529-6537)", "src/pages/KnowledgeHub/children/UploadKnowledgeFile/page.tsx(1586-1594)"], "$id": 1046}, "知识库详情": {"en-US": "Knowledge base details", "de-DE": "Wissensbasisdetails", "$files": ["src/pages/KnowledgeHub/children/KnowledgeDetails/page.tsx(6884-6893)"], "$id": 1047}, "上传成功": {"en-US": "Upload successful", "de-DE": "Upload erfolgreich", "$files": ["src/pages/KnowledgeHub/children/UploadKnowledgeFile/page.tsx(992-1000)"], "$id": 1048}, "选择文件": {"en-US": "Select file", "de-DE": "<PERSON>i ausw<PERSON>hlen", "$files": ["src/pages/KnowledgeHub/children/UploadKnowledgeFile/page.tsx(1806-1814)"], "$id": 1050}, "仅支持单个文件上传，文件格式：PDF、DOCX、TXT、XLSX、CSV": {"en-US": "Only single file uploads are supported, file formats: PDF, DOCX, TXT, XLSX, CSV", "de-DE": "Es wird nur das Hochladen einer einzelnen Datei unterstützt. Dateiformate: PDF, DOCX, TXT, XLSX, CSV", "$files": ["src/pages/KnowledgeHub/children/UploadKnowledgeFile/page.tsx(2482-2522)"], "$id": 1052}, "创建时解析": {"en-US": "Parse on creation", "de-DE": "<PERSON><PERSON> analysieren", "$files": ["src/pages/KnowledgeHub/children/UploadKnowledgeFile/page.tsx(2749-2758)"], "$id": 1053}, "参数详情": {"en-US": "Parameter Details", "de-DE": "Parameterdetails", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/page.tsx(506-514)"], "$id": 1079}, "日志内容": {"en-US": "Log Content", "de-DE": "Protokollinhalt", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/page.tsx(593-601)"], "$id": 1080}, "结果": {"en-US": "Result", "de-DE": "<PERSON><PERSON><PERSON><PERSON>", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/page.tsx(678-684)"], "$id": 1081}, "模型评估日志": {"en-US": "Model Evaluation Log", "de-DE": "Modellevaluierungsprotokoll", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/page.tsx(1176-1186)"], "$id": 1082}, "模型1": {"en-US": "Model 1", "de-DE": "Modell 1", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/constant.ts(119-126)"], "$id": 1083}, "模型1-模型名称": {"en-US": "Model 1 - Model name", "de-DE": "Modell 1 - Modellname", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/constant.ts(194-206)"], "$id": 1084}, "模型2": {"en-US": "Model 2", "de-DE": "Modell 2", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/constant.ts(268-275)"], "$id": 1085}, "模型2-模型名称": {"en-US": "Model 2 - Model Name", "de-DE": "Modell 2 - <PERSON>lname", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/constant.ts(343-355)"], "$id": 1086}, "模型名称": {"en-US": "Model Name", "de-DE": "<PERSON><PERSON><PERSON>", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/constant.ts(547-555)", "src/pages/ModelSetting/pages/DeployedModel/page.tsx(835-843)", "src/pages/ModelSetting/pages/ModelBaseList/page.tsx(2610-2618)", "src/pages/ModelSetting/pages/ModelBaseList/page.tsx(4531-4539)", "src/pages/ModelSetting/pages/SavedModel/page.tsx(4046-4054)", "src/pages/ModelSetting/pages/TrainedModel/page.tsx(1636-1644)", "src/pages/ModelSetting/pages/ThirdPartyModel/components/ConfigModel.tsx(3784-3792)"], "$id": 115}, "评估模型": {"en-US": "Evaluation Model", "de-DE": "Auswertungsmodell", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/page.tsx(9413-9421)"], "$id": 1087}, "选择您想要使用的评估模式": {"en-US": "Select the evaluation mode you want to use", "de-DE": "Wählen Sie den gewünschten Auswertungsmodus", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/page.tsx(9486-9502)"], "$id": 1088}, "裁判模式": {"en-US": "Referee Mode", "de-DE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/page.tsx(9722-9730)"], "$id": 1089}, "使用模型A、模型B和裁判模型进行评估": {"en-US": "Evaluate using Model A, Model B, and Referee Model", "de-DE": "Verwenden Sie Modell A, Modell B und Schiedsrichtermodell zur Bewertung", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/page.tsx(9792-9814)"], "$id": 1090}, "对比模式": {"en-US": "Comparison Mode", "de-DE": "Vergleichsmodus", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/page.tsx(9867-9875)", "src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultDetail.tsx(2676-2684)"], "$id": 1091}, "仅使用模型A和模型B进行对比": {"en-US": "Compare using only Model A and Model B", "de-DE": "Nur Modell A und Modell B für den Vergleich verwenden", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/page.tsx(9937-9955)"], "$id": 1092}, "评估模式": {"en-US": "Evaluation Mode", "de-DE": "Auswertungsmodus", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/page.tsx(10008-10016)", "src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultDetail.tsx(2696-2704)"], "$id": 1093}, "利用裁判模型评估模型A的表现": {"en-US": "Evaluate Model A's performance using the Referee Model", "de-DE": "Nutzen Sie das Schiedsrichtermodell, um die Leistung von Modell A zu bewerten", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/page.tsx(10079-10097)"], "$id": 1094}, "单模型模式": {"en-US": "Single Model Mode", "de-DE": "Einzelmodellmodus", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/page.tsx(10150-10159)", "src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultDetail.tsx(2716-2725)"], "$id": 1095}, "仅使用模型A进行输出": {"en-US": "Output using only Model A", "de-DE": "Nur Modell A für die Ausgabe verwenden", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/page.tsx(10220-10234)"], "$id": 1096}, "数据集选择": {"en-US": "Dataset Selection", "de-DE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/page.tsx(10633-10642)"], "$id": 1097}, "选择要评估的基础数据集": {"en-US": "Select the base dataset for evaluation", "de-DE": "Wählen Sie den zu bewertenden Basisdatensatz aus", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/page.tsx(10722-10737)"], "$id": 1098}, "请选择数据集": {"en-US": "Please select a dataset", "de-DE": "Bitte wählen Si<PERSON> einen Datensatz aus", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/page.tsx(10860-10870)"], "$id": 1099}, "数据集大小": {"en-US": "Dataset Size", "de-DE": "Datensatzgröße", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/page.tsx(11059-11068)"], "$id": 1100}, "配置评估数据集的大小": {"en-US": "Configure the size of the evaluation dataset", "de-DE": "Konfigurieren Sie die Größe des Auswertungsdatensatzes", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/page.tsx(11141-11155)"], "$id": 1101}, "选择全部": {"en-US": "Select All", "de-DE": "Alles auswählen", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/page.tsx(11549-11557)"], "$id": 1102}, "预览设置": {"en-US": "Preview Settings", "de-DE": "Vorschau-Einstellungen", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/page.tsx(12346-12354)"], "$id": 1103}, "设置预览数量并查看部分结果,预览结果在这个页面下方显示,向下滑动查看": {"en-US": "Set the preview count and view partial results. Preview results are displayed below this page. Scroll down to view.", "de-DE": "Legen Sie die Anzahl der Vorschauelemente fest und sehen Si<PERSON> sich Teilergebnisse an. Die Vorschauergebnisse werden unten auf dieser Seite angezeigt. Scrollen Si<PERSON> nach unten, um sie anzusehen.", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/page.tsx(12423-12476)"], "$id": 1104}, "预览数量:": {"en-US": "Preview Quantity:", "de-DE": "Vors<PERSON>uanzahl:", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/page.tsx(12595-12604)"], "$id": 1105}, "预览结果": {"en-US": "Preview Results", "de-DE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/page.tsx(13162-13170)", "src/pages/ModelEvaluation/children/RunModelEvaluation/components/ResultCard.tsx(922-930)"], "$id": 1106}, "提交评估": {"en-US": "Submit Evaluation", "de-DE": "Bewertung absenden", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/page.tsx(13340-13348)", "src/pages/ModelEvaluation/children/RunModelEvaluation/page.tsx(13623-13631)"], "$id": 1107}, "提交所有数据进行完整评估": {"en-US": "Submit All Data for Full Evaluation", "de-DE": "Alle Daten für die vollständige Bewertung absenden", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/page.tsx(13417-13433)"], "$id": 1108}, "量化方式": {"en-US": "Quantization Method", "de-DE": "Quantifizierungsmethode", "$files": ["src/pages/ModelSetting/pages/DeployedModel/page.tsx(939-947)"], "$id": 1055}, "部署框架": {"en-US": "Deployment Framework", "de-DE": "Bereitstellungsframework", "$files": ["src/pages/ModelSetting/pages/DeployedModel/page.tsx(1028-1036)", "src/pages/ModelSetting/pages/DeployedModel/components/ExpandContent.tsx(1630-1638)"], "$id": 299}, "服务地址": {"en-US": "Service Address", "de-DE": "Dienstadresse", "$files": ["src/pages/ModelSetting/pages/DeployedModel/page.tsx(1139-1147)"], "$id": 1056}, "加载到第三方": {"en-US": "Load to Third Party", "de-DE": "In Drittanbieter laden", "$files": ["src/pages/ModelSetting/pages/DeployedModel/page.tsx(1807-1817)"], "$id": 265}, "已部署模型": {"en-US": "Deployed Model List", "de-DE": "Bereits bereitgestellte Modelle", "$files": ["src/pages/ModelSetting/pages/DeployedModel/page.tsx(3143-3152)"], "$id": 1057}, "获取模型列表失败": {"en-US": "Failed to get model list", "de-DE": "Fehler beim Abrufen der Modellliste", "$files": ["src/pages/ModelSetting/pages/ModelBaseList/page.tsx(2047-2059)"], "$id": 1759}, "添加成功": {"en-US": "Add successful", "de-DE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hi<PERSON>", "$files": ["src/pages/ModelSetting/pages/ModelBaseList/page.tsx(2318-2326)", "src/pages/Setting/pages/UserManagement/page.tsx(1892-1900)", "src/pages/Setting/pages/DeployEnv/page.tsx(1446-1454)"], "$id": 325}, "添加失败：": {"en-US": "Add failed:", "de-DE": "Hinzufügen fehlgeschlagen:", "$files": ["src/pages/ModelSetting/pages/ModelBaseList/page.tsx(2367-2376)", "src/pages/Setting/pages/UserManagement/page.tsx(1941-1950)"], "$id": 326}, "模型别名": {"en-US": "<PERSON> <PERSON><PERSON>", "de-DE": "<PERSON><PERSON><PERSON>", "$files": ["src/pages/ModelSetting/pages/ModelBaseList/page.tsx(2515-2523)", "src/pages/ModelSetting/pages/ModelBaseList/page.tsx(4718-4726)", "src/pages/ModelSetting/pages/ModelBaseList/page.tsx(7174-7182)"], "$id": 1058}, "模型路径": {"en-US": "Model Path", "de-DE": "Modelpfad", "$files": ["src/pages/ModelSetting/pages/ModelBaseList/page.tsx(2701-2709)", "src/pages/ModelSetting/pages/ModelBaseList/page.tsx(7596-7604)", "src/pages/ModelSetting/pages/TrainedModel/page.tsx(1727-1735)", "src/pages/ModelSetting/pages/DeployedModel/components/ExpandContent.tsx(601-609)"], "$id": 279}, "架构": {"en-US": "Architecture", "de-DE": "Architektur", "$files": ["src/pages/ModelSetting/pages/ModelBaseList/page.tsx(2792-2798)", "src/pages/ModelSetting/pages/DeployedModel/components/ExpandContent.tsx(855-861)", "src/pages/ModelSetting/pages/SavedModel/components/ExpandContent.tsx(1149-1155)", "src/pages/ModelSetting/pages/TrainedModel/components/ExpandContent.tsx(396-402)"], "$id": 266}, "标签": {"en-US": "Tags", "de-DE": "Tags", "$files": ["src/pages/ModelSetting/pages/ModelBaseList/page.tsx(2889-2895)", "src/pages/ModelSetting/pages/DeployedModel/components/ExpandContent.tsx(1109-1115)", "src/pages/ModelSetting/pages/SavedModel/components/ExpandContent.tsx(1427-1433)", "src/pages/ModelSetting/pages/TrainedModel/components/ExpandContent.tsx(602-608)"], "$id": 267}, "训练框架": {"en-US": "Training Framework", "de-DE": "Trainingsframework", "$files": ["src/pages/ModelSetting/pages/ModelBaseList/page.tsx(3230-3238)", "src/pages/ModelSetting/pages/ModelBaseList/page.tsx(5094-5102)", "src/pages/ModelSetting/pages/DeployedModel/components/ExpandContent.tsx(1358-1366)", "src/pages/ModelSetting/pages/SavedModel/components/ExpandContent.tsx(1701-1709)", "src/pages/ModelSetting/pages/TrainedModel/components/ExpandContent.tsx(883-891)", "src/pages/ModelSetting/pages/TrainedModel/components/ExpandContent.tsx(963-971)"], "$id": 268}, "磁盘占用空间": {"en-US": "Disk Space Occupancy", "de-DE": "Plattenbelegung", "$files": ["src/pages/ModelSetting/pages/ModelBaseList/page.tsx(3337-3347)", "src/pages/ModelSetting/pages/SavedModel/page.tsx(4516-4526)", "src/pages/ModelSetting/pages/TrainedModel/page.tsx(2031-2041)"], "$id": 269}, "请输入模型名称": {"en-US": "Please enter the model name", "de-DE": "<PERSON>te geben Si<PERSON> den Modellnamen ein", "$files": ["src/pages/ModelSetting/pages/ModelBaseList/page.tsx(4596-4607)"], "$id": 1760}, "请输入模型别名": {"en-US": "Please enter the model alias", "de-DE": "<PERSON><PERSON> geben Si<PERSON> den Modellalias ein", "$files": ["src/pages/ModelSetting/pages/ModelBaseList/page.tsx(4785-4796)", "src/pages/ModelSetting/pages/ModelBaseList/page.tsx(7263-7274)"], "$id": 1060}, "模型类型": {"en-US": "Model Type", "de-DE": "Modelltyp", "$files": ["src/pages/ModelSetting/pages/ModelBaseList/page.tsx(4907-4915)", "src/pages/ModelSetting/pages/DeployedModel/components/ExpandContent.tsx(768-776)", "src/pages/ModelSetting/pages/ThirdPartyModel/components/ConfigModel.tsx(3488-3496)", "src/pages/ModelSetting/pages/SavedModel/components/ExpandContent.tsx(1054-1062)", "src/pages/ModelSetting/pages/ModelBaseList/components/ExpandContent.tsx(325-333)", "src/pages/ModelSetting/pages/TrainedModel/components/ExpandContent.tsx(325-333)"], "$id": 292}, "请输入模型类型": {"en-US": "Please enter the model type", "de-DE": "<PERSON>te geben Si<PERSON> den Modelltyp ein", "$files": ["src/pages/ModelSetting/pages/ModelBaseList/page.tsx(4972-4983)"], "$id": 1761}, "请输入训练框架": {"en-US": "Please enter the training framework", "de-DE": "Bitte geben Sie das Trainingsframework ein", "$files": ["src/pages/ModelSetting/pages/ModelBaseList/page.tsx(5167-5178)"], "$id": 1762}, "基础模型管理": {"en-US": "Base Model List", "de-DE": "Grundlagenmodellverwaltung", "$files": ["src/pages/ModelSetting/pages/ModelBaseList/page.tsx(5710-5720)"], "$id": 1059}, "编辑模型": {"en-US": "Edit Model", "de-DE": "<PERSON><PERSON> bear<PERSON>ten", "$files": ["src/pages/ModelSetting/pages/ModelBaseList/page.tsx(6907-6915)", "src/pages/ModelSetting/pages/ThirdPartyModel/components/ConfigModel.tsx(2831-2839)"], "$id": 271}, "基础模型名称": {"en-US": "Base Model Name", "de-DE": "Grundlagenmodellname", "$files": ["src/pages/ModelSetting/pages/ModelBaseList/page.tsx(7384-7394)"], "$id": 1061}, "请输入基础模型名称": {"en-US": "Please enter the base model name", "de-DE": "Bitte geben Sie den Grundlagenmodellnamen ein", "$files": ["src/pages/ModelSetting/pages/ModelBaseList/page.tsx(7473-7486)"], "$id": 1062}, "请输入模型路径": {"en-US": "Please enter the model path", "de-DE": "<PERSON>te geben Si<PERSON> den Modellpfad ein", "$files": ["src/pages/ModelSetting/pages/ModelBaseList/page.tsx(7683-7694)"], "$id": 1063}, "是否是VL模型": {"en-US": "Is it a VL model", "de-DE": "Das Modell VL", "$files": ["src/pages/ModelSetting/pages/ModelBaseList/page.tsx(7792-7803)"], "$id": 1812}, "是": {"en-US": "correct", "de-DE": "<PERSON>a.", "$files": ["src/pages/ModelSetting/pages/ModelBaseList/page.tsx(7860-7865)"], "$id": 1813}, "否": {"en-US": "deny", "de-DE": "<PERSON><PERSON>", "$files": ["src/pages/ModelSetting/pages/ModelBaseList/page.tsx(7887-7892)"], "$id": 1814}, "模版": {"en-US": "Template", "de-DE": "Vorlage", "$files": ["src/pages/ModelSetting/pages/ModelBaseList/page.tsx(7949-7955)"], "$id": 273}, "提供商": {"en-US": "Provider", "de-DE": "<PERSON><PERSON><PERSON>", "$files": ["src/pages/ModelSetting/pages/ThirdPartyModel/page.tsx(2717-2724)", "src/pages/ModelSetting/pages/ThirdPartyModel/components/CardItem.tsx(2621-2628)", "src/pages/ModelSetting/pages/ThirdPartyModel/components/ConfigModel.tsx(3196-3203)"], "$id": 278}, "模型删除成功": {"en-US": "Model deleted successfully", "de-DE": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "$files": ["src/pages/ModelSetting/pages/ThirdPartyModel/page.tsx(5502-5512)"], "$id": 1066}, "模型删除失败": {"en-US": "Model deletion failed", "de-DE": "<PERSON>l konnte nicht gelöscht werden", "$files": ["src/pages/ModelSetting/pages/ThirdPartyModel/page.tsx(5640-5650)"], "$id": 1067}, "模型编辑成功": {"en-US": "<PERSON> edited successfully", "de-DE": "<PERSON>l erfolgreich bearbei<PERSON>t", "$files": ["src/pages/ModelSetting/pages/ThirdPartyModel/page.tsx(6129-6139)"], "$id": 1068}, "模型编辑失败:": {"en-US": "Model editing failed:", "de-DE": "Modellbearbeitung fehlgeschlagen:", "$files": ["src/pages/ModelSetting/pages/ThirdPartyModel/page.tsx(6184-6195)"], "$id": 1069}, "模型添加成功": {"en-US": "Model added successfully", "de-DE": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hinz<PERSON>gt", "$files": ["src/pages/ModelSetting/pages/ThirdPartyModel/page.tsx(6336-6346)"], "$id": 1070}, "模型添加失败:": {"en-US": "Model addition failed:", "de-DE": "Modell konnte nicht hinzugefügt werden:", "$files": ["src/pages/ModelSetting/pages/ThirdPartyModel/page.tsx(6391-6402)"], "$id": 1071}, "操作失败": {"en-US": "Operation failed", "de-DE": "Vorgang fehlgeschlagen", "$files": ["src/pages/ModelSetting/pages/ThirdPartyModel/page.tsx(6619-6627)"], "$id": 1072}, "测试失败": {"en-US": "Test failed", "de-DE": "Test fehlgeschlagen", "$files": ["src/pages/ModelSetting/pages/ThirdPartyModel/page.tsx(6916-6924)", "src/pages/ModelSetting/pages/ThirdPartyModel/components/ConfigModel.tsx(2114-2122)"], "$id": 1073}, "模型设置": {"en-US": "Third-Party Model List", "de-DE": "Modelleinstellungen", "$files": ["src/pages/ModelSetting/pages/ThirdPartyModel/page.tsx(7914-7922)"], "$id": 1075}, "已保存模型": {"en-US": "Saved Model List", "de-DE": "Gespeicherte Modelle", "$files": ["src/pages/ModelSetting/pages/SavedModel/page.tsx(2488-2497)"], "$id": 274}, "部署": {"en-US": "Deploy", "de-DE": "Bereitstellen", "$files": ["src/pages/ModelSetting/pages/SavedModel/page.tsx(5312-5318)"], "$id": 276}, "序号": {"en-US": "Serial Number", "de-DE": "Seriennummer", "$files": ["src/pages/Datasets/children/DatasetsDetails/components/DataPart.tsx(2253-2259)"], "$id": 1110}, "获取数据集日志成功：": {"en-US": "Successfully Retrieved Dataset Logs:", "de-DE": "Datensatzprotokoll erfolgreich abgerufen:", "$files": ["src/pages/Datasets/children/DatasetsDetails/components/LogPart.tsx(589-603)"], "$id": 1111}, "获取数据集日志失败：": {"en-US": "Failed to Retrieve Dataset Logs:", "de-DE": "Fehler beim Abrufen des Datensatzprotokolls:", "$files": ["src/pages/Datasets/children/DatasetsDetails/components/LogPart.tsx(705-719)"], "$id": 1112}, "没有匹配的日志记录": {"en-US": "No Matching Log Records", "de-DE": "<PERSON>ine übereinstimmenden Protokolleinträge", "$files": ["src/pages/Datasets/children/DatasetsDetails/components/LogPart.tsx(1065-1078)", "src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultLogs.tsx(1001-1014)", "src/pages/ModelSetting/pages/SavedModel/pages/log/page.tsx(3714-3727)", "src/pages/ModelSetting/pages/TrainedModel/pages/log/page.tsx(3744-3757)"], "$id": 1113}, "当前版本暂不支持添加方法功能。如需解锁此功能及更多专业服务，欢迎联系团队升级至专业版。": {"en-US": "The current version does not support the addition of method functionality. To unlock this feature and access more professional services, please contact the team to upgrade to the Professional Edition.", "de-DE": "Die aktuelle Version unterstützt die Funktion \\\"Methode hinzufügen\\\" vorübergehend nicht. Wenn Sie diese Funktion und weitere professionelle Dienste freischalten möchten, kontaktieren Sie bitte das Team, um auf die professionelle Version upzugraden.", "$files": ["src/pages/Setting/pages/TrainMethod/page.tsx(844-900)"], "$id": 288}, "训练方法": {"en-US": "Training Method", "de-DE": "Trainingsmethode", "$files": ["src/pages/Setting/pages/TrainMethod/page.tsx(1237-1245)"], "$id": 289}, "添加方法": {"en-US": "Add Method", "de-DE": "Methode hinzufügen", "$files": ["src/pages/Setting/pages/TrainMethod/page.tsx(1397-1405)"], "$id": 1123}, "训练文件": {"en-US": "Training File", "de-DE": "Trainingsdatei", "$files": ["src/pages/Setting/pages/TrainMethod/page.tsx(1826-1834)"], "$id": 290}, "用户管理": {"en-US": "User management", "de-DE": "Benutzerverwaltung", "$files": ["src/pages/Setting/pages/UserManagement/page.tsx(2360-2368)"], "$id": 327}, "添加用户": {"en-US": "Add User", "de-DE": "Benutzer hinzufügen", "$files": ["src/pages/Setting/pages/UserManagement/page.tsx(2570-2578)", "src/pages/Setting/pages/UserManagement/page.tsx(4534-4542)"], "$id": 114}, "自己": {"en-US": "Self", "de-DE": "Se<PERSON>bst", "$files": ["src/pages/Setting/pages/UserManagement/page.tsx(3475-3481)"], "$id": 329}, "项目代码": {"en-US": "Project code", "de-DE": "Projektcode", "$files": ["src/pages/Setting/pages/UserManagement/page.tsx(3647-3655)"], "$id": 1561}, "手机号": {"en-US": "Phone Number", "de-DE": "<PERSON><PERSON><PERSON><PERSON>", "$files": ["src/pages/Setting/pages/UserManagement/page.tsx(3782-3789)"], "$id": 331}, "是否删除该用户？": {"en-US": "Delete this user?", "de-DE": "Möchten Sie diesen Benutzer löschen?", "$files": ["src/pages/Setting/pages/UserManagement/page.tsx(4120-4132)"], "$id": 183}, "该操作将永久删除该用户，请谨慎操作。": {"en-US": "This operation will permanently delete the user. Please proceed with caution.", "de-DE": "Diese Aktion wird den Benutzer dauerhaft löschen. Bitte gehen Sie mit Vorsicht vor.", "$files": ["src/pages/Setting/pages/UserManagement/page.tsx(4175-4220)"], "$id": 184}, "请输入用户名": {"en-US": "Please enter username", "de-DE": "Bitte Benutzernamen eingeben", "$files": ["src/pages/Setting/pages/UserManagement/page.tsx(4951-4961)"], "$id": 333}, "昵称": {"en-US": "Nickname", "de-DE": "Spitzname", "$files": ["src/pages/Setting/pages/UserManagement/page.tsx(5053-5059)"], "$id": 334}, "请输入昵称": {"en-US": "Please enter nickname", "de-DE": "Bitte Spitznamen eingeben", "$files": ["src/pages/Setting/pages/UserManagement/page.tsx(5137-5146)"], "$id": 335}, "电话号": {"en-US": "Phone number", "de-DE": "Telefonnummer", "$files": ["src/pages/Setting/pages/UserManagement/page.tsx(5427-5434)"], "$id": 338}, "邮箱": {"en-US": "Email", "de-DE": "E-Mail", "$files": ["src/pages/Setting/pages/UserManagement/page.tsx(5524-5530)"], "$id": 339}, "微调数据集": {"en-US": "Fine-tuning Dataset", "de-DE": "Feinabstimmungsdatensatz", "$files": ["src/pages/ModelSetting/pages/TrainedModel/page.tsx(1909-1918)"], "$id": 280}, "获取模型配置：": {"en-US": "Fetching model configuration:", "de-DE": "Modellkonfiguration abrufen:", "$files": ["src/pages/ModelSetting/pages/TrainedModel/page.tsx(3662-3673)"], "$id": 1077}, "获取模型配置失败：": {"en-US": "Failed to fetch model configuration:", "de-DE": "Abrufen der Modellkonfiguration fehlgeschlagen:", "$files": ["src/pages/ModelSetting/pages/TrainedModel/page.tsx(4066-4079)"], "$id": 1078}, "已训练模型": {"en-US": "Trained Models", "de-DE": "Trainierte Modelle", "$files": ["src/pages/ModelSetting/pages/TrainedModel/page.tsx(4447-4456)"], "$id": 1590}, "请选择日志": {"en-US": "Please select a log", "de-DE": "Bitte wählen Sie das Protokoll aus", "$files": ["src/pages/FineTuningLog/children/CompareLogs/components/TaskDetails.tsx(1022-1031)"], "$id": 1114}, "日志概览": {"en-US": "Log Overview", "de-DE": "Protokollübersicht", "$files": ["src/pages/FineTuningLog/children/CompareLogs/components/TaskLog.tsx(1413-1421)", "src/pages/FineTuningLog/children/LogDetails/components/TaskLog.tsx(1474-1482)"], "$id": 1119}, "完整日志": {"en-US": "Full Log Content", "de-DE": "Vollständiges Protokoll", "$files": ["src/pages/FineTuningLog/children/CompareLogs/components/TaskLog.tsx(2551-2559)", "src/pages/FineTuningLog/children/LogDetails/components/TaskLog.tsx(2297-2305)"], "$id": 1120}, "获取概览日志失败：": {"en-US": "Failed to fetch overview log:", "de-DE": "Abrufen des Übersichtsprotokolls fehlgeschlagen:", "$files": ["src/pages/FineTuningLog/children/LogDetails/components/TaskLog.tsx(967-980)"], "$id": 1117}, "获取训练进度失败：": {"en-US": "Failed to fetch training progress:", "de-DE": "Abrufen des Trainingsfortschritts fehlgeschlagen:", "$files": ["src/pages/FineTuningLog/children/LogDetails/components/TaskLog.tsx(1233-1246)"], "$id": 1118}, "更新成功": {"en-US": "Update Successful", "de-DE": "Aktualisierung erfolgreich", "$files": ["src/pages/Setting/pages/DeployEnv/page.tsx(1919-1927)"], "$id": 1121}, "部署环境": {"en-US": "Deployment Environment", "de-DE": "Bereitstellungsumgebung", "$files": ["src/pages/Setting/pages/DeployEnv/page.tsx(2455-2463)"], "$id": 283}, "添加环境": {"en-US": "Add Environment", "de-DE": "Umgebung hinzufügen", "$files": ["src/pages/Setting/pages/DeployEnv/page.tsx(2654-2662)", "src/pages/Setting/pages/DeployEnv/page.tsx(4979-4987)"], "$id": 284}, "环境名称": {"en-US": "Environment Name", "de-DE": "Umgebungsname", "$files": ["src/pages/Setting/pages/DeployEnv/page.tsx(5367-5375)"], "$id": 285}, "环境地址": {"en-US": "Environment Address", "de-DE": "Umgebungsadresse", "$files": ["src/pages/Setting/pages/DeployEnv/page.tsx(5555-5563)"], "$id": 286}, "请输入环境地址": {"en-US": "Please Enter the Environment Address", "de-DE": "<PERSON>te geben Sie die Umgebungsadresse ein", "$files": ["src/pages/Setting/pages/DeployEnv/page.tsx(5621-5632)"], "$id": 1122}, "模型选择": {"en-US": "Model Selection", "de-DE": "<PERSON><PERSON><PERSON><PERSON>", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/components/ModelInput.tsx(1185-1193)"], "$id": 1163}, "选择要评估的模型和知识库": {"en-US": "Select the model and knowledge base to evaluate", "de-DE": "Wählen Sie das zu bewertende Modell und die Wissensdatenbank aus", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/components/ModelInput.tsx(1252-1268)"], "$id": 1164}, "模型 A": {"en-US": "Model A", "de-DE": "Modell A", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/components/ModelInput.tsx(1452-1460)", "src/pages/ModelEvaluation/children/RunModelEvaluation/components/ResultCard.tsx(2066-2074)", "src/pages/ModelEvaluation/children/RunModelEvaluation/components/ResultCard.tsx(3769-3777)"], "$id": 1165}, "请选择模型 A": {"en-US": "Please select Model A", "de-DE": "Bitte Modell A auswählen", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/components/ModelInput.tsx(1570-1581)"], "$id": 1166}, "选择模型 A": {"en-US": "Select Model A", "de-DE": "Modell A auswählen", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/components/ModelInput.tsx(1635-1645)"], "$id": 1167}, "模型 A 知识库": {"en-US": "Model A Knowledge Base", "de-DE": "Wissensdatenbank von Modell A", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/components/ModelInput.tsx(2119-2131)"], "$id": 1168}, "无知识库": {"en-US": "No knowledge base", "de-DE": "Keine Wissensdatenbank", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/components/ModelInput.tsx(2232-2240)", "src/pages/ModelEvaluation/children/RunModelEvaluation/components/ModelInput.tsx(3350-3358)", "src/pages/ModelEvaluation/children/RunModelEvaluation/components/ModelInput.tsx(4546-4554)"], "$id": 1169}, "模型 B": {"en-US": "Model B", "de-DE": "Modell B", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/components/ModelInput.tsx(2526-2534)", "src/pages/ModelEvaluation/children/RunModelEvaluation/components/ResultCard.tsx(2907-2915)"], "$id": 1170}, "请选择模型 B": {"en-US": "Please select Model B", "de-DE": "Bitte Modell B auswählen", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/components/ModelInput.tsx(2650-2661)"], "$id": 1171}, "选择模型 B": {"en-US": "Select Model B", "de-DE": "Modell B auswählen", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/components/ModelInput.tsx(2719-2729)"], "$id": 1172}, "模型 B 知识库": {"en-US": "Model B Knowledge Base", "de-DE": "Wissensdatenbank von Modell B", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/components/ModelInput.tsx(3231-3243)"], "$id": 1173}, "请选择裁判模型": {"en-US": "Please select the referee model", "de-DE": "Bitte Schiedsrichtermodell auswählen", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/components/ModelInput.tsx(3810-3821)"], "$id": 1174}, "选择裁判模型": {"en-US": "Select the referee model", "de-DE": "Schiedsrichtermodell auswählen", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/components/ModelInput.tsx(3912-3922)"], "$id": 1175}, "裁判模型知识库": {"en-US": "Referee Model Knowledge Base", "de-DE": "Wissensdatenbank des Schiedsrichtermodells", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/components/ModelInput.tsx(4424-4435)"], "$id": 1176}, "模型提示词": {"en-US": "Model Prompt", "de-DE": "Modell-Prompt", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/components/Prompts.tsx(767-776)", "src/pages/ModelEvaluation/children/RunModelEvaluation/components/Prompts.tsx(2415-2424)", "src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultDetail.tsx(4143-4152)"], "$id": 1147}, "配置模型A和模型B的提示词": {"en-US": "Configure the prompts for Model A and Model B", "de-DE": "Konfigurieren Sie die Prompts für Modell A und Modell B", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/components/Prompts.tsx(849-866)", "src/pages/ModelEvaluation/children/RunModelEvaluation/components/Prompts.tsx(2509-2526)"], "$id": 1177}, "系统提示词": {"en-US": "System Prompt", "de-DE": "Systemprompt", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/components/Prompts.tsx(1036-1045)", "src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultDetail.tsx(4328-4337)"], "$id": 1148}, "输入系统提示词...": {"en-US": "Enter system prompt...", "de-DE": "<PERSON><PERSON><PERSON> Si<PERSON> den Systemprompt ein...", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/components/Prompts.tsx(1373-1387)"], "$id": 1179}, "系统提示词用于设置模型的行为和角色": {"en-US": "The system prompt is used to set the behavior and role of the model", "de-DE": "Der Systemprompt wird verwendet, um das Verhalten und die Rolle des Modells festzulegen", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/components/Prompts.tsx(1688-1709)"], "$id": 1180}, "用户提示词是您想要模型回答的实际问题或任务": {"en-US": "The user prompt is the actual question or task you want the model to answer", "de-DE": "Der Benutzerprompt ist die eigentliche Frage oder Aufgabe, die Sie vom Modell beantwortet haben möchten", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/components/Prompts.tsx(1926-1951)"], "$id": 1181}, "用户提示词": {"en-US": "User Prompt", "de-DE": "Benutzerhinweis", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/components/Prompts.tsx(1979-1988)", "src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultDetail.tsx(4555-4564)"], "$id": 1149}, "裁判系统提示词": {"en-US": "Referee System Prompt", "de-DE": "Schiedsrichtersystem-Hinweis", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/components/Prompts.tsx(3304-3315)", "src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultDetail.tsx(5125-5136)"], "$id": 1151}, "设置裁判模型的行为和评估标准": {"en-US": "Set the behavior and evaluation criteria for the referee model", "de-DE": "Verhalten und Bewertungsstandards des Schiedsrichtermodells einstellen", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/components/Prompts.tsx(3345-3363)"], "$id": 1183}, "裁判用户提示词": {"en-US": "Referee User Prompt", "de-DE": "Schiedsrichter-Benutzerhinweis", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/components/Prompts.tsx(3966-3977)", "src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultDetail.tsx(5362-5373)"], "$id": 1152}, "指导裁判模型如何评估模型的回答": {"en-US": "Guide the referee model on how to evaluate the model's responses", "de-DE": "Anleitung für das Schiedsrichtermodell zur Bewertung der Modellantworten", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/components/Prompts.tsx(4007-4026)"], "$id": 1185}, "AI文本": {"en-US": "AI Text", "de-DE": "KI-Text", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/components/Prompts.tsx(4608-4616)"], "$id": 1186}, "请选择": {"en-US": "Please Select", "de-DE": "Bitte auswählen", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/components/Prompts.tsx(5400-5407)"], "$id": 1187}, "评估结果:": {"en-US": "Evaluation Result:", "de-DE": "Bewertungsergebnis:", "$files": ["src/pages/ModelEvaluation/children/RunModelEvaluation/components/ResultCard.tsx(4496-4505)"], "$id": 1188}, "名字：": {"en-US": "Name:", "de-DE": "Name:", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultDetail.tsx(1131-1138)"], "$id": 1139}, "知识库：": {"en-US": "Knowledge Base:", "de-DE": "Wissensbasis:", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultDetail.tsx(1223-1231)"], "$id": 1140}, "无": {"en-US": "None", "de-DE": "<PERSON><PERSON>", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultDetail.tsx(1288-1293)", "src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/components/CollapsibleText.tsx(714-719)"], "$id": 1141}, "评估模式：": {"en-US": "Evaluation Mode:", "de-DE": "Bewertungsmodus:", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultDetail.tsx(2912-2921)"], "$id": 1142}, "数据集信息：": {"en-US": "Dataset Information:", "de-DE": "Dataset-Informationen:", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultDetail.tsx(3149-3159)"], "$id": 1143}, "评估总条数：": {"en-US": "Total Evaluation Count:", "de-DE": "Gesamtanzahl der Bewertungen:", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultDetail.tsx(3451-3461)"], "$id": 1144}, "全部": {"en-US": "All", "de-DE": "Alle", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultDetail.tsx(3573-3579)"], "$id": 1145}, "复制参数": {"en-US": "Copy Parameters", "de-DE": "Parameter kopieren", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultDetail.tsx(3832-3840)"], "$id": 1146}, "裁判模型提示词": {"en-US": "Referee Model Prompt", "de-DE": "Schiedsrichtermodell-Hin<PERSON>s", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultDetail.tsx(4915-4926)"], "$id": 1150}, "版本": {"en-US": "Version", "de-DE": "Version", "$files": ["src/pages/ModelSetting/pages/DeployedModel/components/ExpandContent.tsx(685-691)", "src/pages/ModelSetting/pages/SavedModel/components/ExpandContent.tsx(963-969)", "src/pages/ModelSetting/pages/ModelBaseList/components/ExpandContent.tsx(258-264)", "src/pages/ModelSetting/pages/TrainedModel/components/ExpandContent.tsx(258-264)"], "$id": 291}, "语言": {"en-US": "Language", "de-DE": "<PERSON><PERSON><PERSON>", "$files": ["src/pages/ModelSetting/pages/DeployedModel/components/ExpandContent.tsx(944-950)", "src/pages/ModelSetting/pages/SavedModel/components/ExpandContent.tsx(1246-1252)", "src/pages/ModelSetting/pages/ModelBaseList/components/ExpandContent.tsx(559-565)", "src/pages/ModelSetting/pages/TrainedModel/components/ExpandContent.tsx(469-475)"], "$id": 293}, "许可证": {"en-US": "License", "de-DE": "<PERSON><PERSON><PERSON>", "$files": ["src/pages/ModelSetting/pages/DeployedModel/components/ExpandContent.tsx(1189-1196)", "src/pages/ModelSetting/pages/SavedModel/components/ExpandContent.tsx(1515-1522)", "src/pages/ModelSetting/pages/ModelBaseList/components/ExpandContent.tsx(692-699)", "src/pages/ModelSetting/pages/TrainedModel/components/ExpandContent.tsx(666-673)"], "$id": 295}, "衡量标准": {"en-US": "Metrics", "de-DE": "Maßstab", "$files": ["src/pages/ModelSetting/pages/DeployedModel/components/ExpandContent.tsx(1273-1281)", "src/pages/ModelSetting/pages/SavedModel/components/ExpandContent.tsx(1607-1615)", "src/pages/ModelSetting/pages/TrainedModel/components/ExpandContent.tsx(734-742)"], "$id": 296}, "保存的格式": {"en-US": "Saved Format", "de-DE": "Gespeichertes Format", "$files": ["src/pages/ModelSetting/pages/DeployedModel/components/ExpandContent.tsx(1453-1462)"], "$id": 297}, "文件路径": {"en-US": "File Path", "de-DE": "Dateipfad", "$files": ["src/pages/ModelSetting/pages/DeployedModel/components/ExpandContent.tsx(1544-1552)"], "$id": 298}, "接口地址": {"en-US": "API Address", "de-DE": "Schnittstellenadresse", "$files": ["src/pages/ModelSetting/pages/DeployedModel/components/ExpandContent.tsx(1727-1735)", "src/pages/ModelSetting/pages/ThirdPartyModel/components/ConfigModel.tsx(4009-4017)"], "$id": 300}, "延迟": {"en-US": "Latency", "de-DE": "<PERSON><PERSON>", "$files": ["src/pages/ModelSetting/pages/DeployedModel/components/ExpandContent.tsx(1816-1822)"], "$id": 301}, "限制": {"en-US": "Limitations", "de-DE": "Einschränkungen", "$files": ["src/pages/ModelSetting/pages/DeployedModel/components/ExpandContent.tsx(1899-1905)", "src/pages/ModelSetting/pages/SavedModel/components/ExpandContent.tsx(1902-1908)", "src/pages/ModelSetting/pages/ModelBaseList/components/ExpandContent.tsx(760-766)", "src/pages/ModelSetting/pages/TrainedModel/components/ExpandContent.tsx(1042-1048)"], "$id": 302}, "作者": {"en-US": "Author", "de-DE": "Autor", "$files": ["src/pages/ModelSetting/pages/DeployedModel/components/ExpandContent.tsx(1986-1992)", "src/pages/ModelSetting/pages/SavedModel/components/ExpandContent.tsx(1997-2003)", "src/pages/ModelSetting/pages/ModelBaseList/components/ExpandContent.tsx(831-837)", "src/pages/ModelSetting/pages/TrainedModel/components/ExpandContent.tsx(1113-1119)"], "$id": 303}, "连接测试成功": {"en-US": "Connection Test Successful", "de-DE": "Verbindungstest erfolgreich", "$files": ["src/pages/ModelSetting/pages/ThirdPartyModel/components/CardItem.tsx(1131-1141)"], "$id": 1124}, "连接测试失败": {"en-US": "Connection Test Failed", "de-DE": "Verbindungstest fehlgeschlagen", "$files": ["src/pages/ModelSetting/pages/ThirdPartyModel/components/CardItem.tsx(1214-1224)", "src/pages/ModelSetting/pages/ThirdPartyModel/components/CardItem.tsx(1312-1322)"], "$id": 1125}, "私有化": {"en-US": "Privatization", "de-DE": "Privat", "$files": ["src/pages/ModelSetting/pages/ThirdPartyModel/components/CardItem.tsx(2517-2524)", "src/pages/ModelSetting/pages/ThirdPartyModel/components/ConfigModel.tsx(4544-4551)"], "$id": 1126}, "请求地址": {"en-US": "Request Address", "de-DE": "Anfrageadresse", "$files": ["src/pages/ModelSetting/pages/ThirdPartyModel/components/CardItem.tsx(2906-2914)"], "$id": 1127}, "测试连接": {"en-US": "Test Connection", "de-DE": "Verbindung testen", "$files": ["src/pages/ModelSetting/pages/ThirdPartyModel/components/CardItem.tsx(3406-3414)", "src/pages/ModelSetting/pages/ThirdPartyModel/components/ConfigModel.tsx(4865-4873)"], "$id": 1128}, "未测试": {"en-US": "Not Tested", "de-DE": "<PERSON><PERSON> get<PERSON>t", "$files": ["src/pages/ModelSetting/pages/ThirdPartyModel/components/ConfigModel.tsx(2071-2078)"], "$id": 1129}, "测试通过": {"en-US": "Test Passed", "de-DE": "Test bestanden", "$files": ["src/pages/ModelSetting/pages/ThirdPartyModel/components/ConfigModel.tsx(2092-2100)"], "$id": 1130}, "测试中...": {"en-US": "Testing...", "de-DE": "Wird getestet...", "$files": ["src/pages/ModelSetting/pages/ThirdPartyModel/components/ConfigModel.tsx(2136-2146)"], "$id": 1131}, "请选择提供商": {"en-US": "Please Select a Provider", "de-DE": "Bitte wählen Si<PERSON> einen Anbieter aus", "$files": ["src/pages/ModelSetting/pages/ThirdPartyModel/components/ConfigModel.tsx(3251-3261)", "src/pages/ModelSetting/pages/ThirdPartyModel/components/ConfigModel.tsx(3311-3321)"], "$id": 1132}, "请选择模型类型": {"en-US": "Please Select a Model Type", "de-DE": "Bitte wählen Sie den Modelltyp aus", "$files": ["src/pages/ModelSetting/pages/ThirdPartyModel/components/ConfigModel.tsx(3544-3555)", "src/pages/ModelSetting/pages/ThirdPartyModel/components/ConfigModel.tsx(3605-3616)"], "$id": 1133}, "请填写模型": {"en-US": "Please Fill in the Model", "de-DE": "Bitte füllen Sie das Modell aus", "$files": ["src/pages/ModelSetting/pages/ThirdPartyModel/components/ConfigModel.tsx(3886-3895)"], "$id": 1134}, "请输入接口地址": {"en-US": "Please Enter the API Address", "de-DE": "Bitte geben Sie die Schnittstellenadresse ein", "$files": ["src/pages/ModelSetting/pages/ThirdPartyModel/components/ConfigModel.tsx(4065-4076)", "src/pages/ModelSetting/pages/ThirdPartyModel/components/ConfigModel.tsx(4113-4124)"], "$id": 1135}, "API密钥": {"en-US": "API Key", "de-DE": "API-Schlüssel", "$files": ["src/pages/ModelSetting/pages/ThirdPartyModel/components/ConfigModel.tsx(4192-4201)"], "$id": 1136}, "如需修改请输入新密钥": {"en-US": "If You Need to Modify, Please Enter a New Key", "de-DE": "Falls Änderungen nötig sind, geben Si<PERSON> einen neuen Schlüssel ein", "$files": ["src/pages/ModelSetting/pages/ThirdPartyModel/components/ConfigModel.tsx(4284-4298)"], "$id": 1137}, "请输入API密钥": {"en-US": "Please enter the API key", "de-DE": "Bitte API-Schlüssel eingeben", "$files": ["src/pages/ModelSetting/pages/ThirdPartyModel/components/ConfigModel.tsx(4302-4314)"], "$id": 1138}, "筛选条件": {"en-US": "<PERSON><PERSON>", "de-DE": "Filterkriterien", "$files": ["src/pages/ModelSetting/pages/ThirdPartyModel/components/filter-component.tsx(2973-2981)"], "$id": 1599}, "已选择 {count} 项": {"en-US": "Selected {count} items", "de-DE": "{count} Elemente ausgewählt", "$files": ["src/pages/ModelSetting/pages/ThirdPartyModel/components/filter-component.tsx(3640-3657)"], "$id": 1600}, "清空筛选": {"en-US": "Clear Filters", "de-DE": "<PERSON><PERSON>", "$files": ["src/pages/ModelSetting/pages/ThirdPartyModel/components/filter-component.tsx(3949-3957)"], "$id": 1601}, "搜索": {"en-US": "Search", "de-DE": "<PERSON><PERSON>", "$files": ["src/pages/ModelSetting/pages/ThirdPartyModel/components/filter-component.tsx(5439-5445)"], "$id": 1602}, "保存路径": {"en-US": "Save Path", "de-DE": "Speicherpfad", "$files": ["src/pages/ModelSetting/pages/SavedModel/components/ExpandContent.tsx(683-691)", "src/pages/ModelSetting/pages/TrainedModel/pages/log/page.tsx(582-590)"], "$id": 1193}, "LoRA适配器路径": {"en-US": "LoRA Adapter Path", "de-DE": "LoRA-Adapter-Pfad", "$files": ["src/pages/ModelSetting/pages/SavedModel/components/ExpandContent.tsx(770-783)", "src/pages/ModelSetting/pages/TrainedModel/pages/log/page.tsx(752-765)"], "$id": 1195}, "基础模型路径": {"en-US": "Base Model Path", "de-DE": "Grundmodellpfad", "$files": ["src/pages/ModelSetting/pages/SavedModel/components/ExpandContent.tsx(869-879)", "src/pages/ModelSetting/pages/TrainedModel/pages/log/page.tsx(661-671)"], "$id": 1194}, "保存格式": {"en-US": "Save Format", "de-DE": "Speicherformat", "$files": ["src/pages/ModelSetting/pages/SavedModel/components/ExpandContent.tsx(1804-1812)"], "$id": 307}, "预训练数据集": {"en-US": "Pre-training Dataset", "de-DE": "Vortrainierte Datensätze", "$files": ["src/pages/ModelSetting/pages/ModelBaseList/components/ExpandContent.tsx(396-406)"], "$id": 305}, "训练目标": {"en-US": "Training Objective", "de-DE": "Trainingsziel", "$files": ["src/pages/ModelSetting/pages/ModelBaseList/components/ExpandContent.tsx(479-487)"], "$id": 306}, "微调目标": {"en-US": "Fine-tuning Objective", "de-DE": "Feinabstimmungsziel", "$files": ["src/pages/ModelSetting/pages/TrainedModel/components/ExpandContent.tsx(803-811)"], "$id": 308}, "训练超参": {"en-US": "Training Hyperparameters", "de-DE": "Trainings-Hyperparameter", "$files": ["src/pages/ModelSetting/pages/TrainedModel/components/ExpandContent.tsx(1252-1260)"], "$id": 309}, "目前您使用的是免费版本，无法删除环境，请前往官网购买专业版。": {"en-US": "You are currently using the free version, which does not allow environment deletion. Please visit the official website to purchase the professional version.", "de-DE": "Sie verwenden derzeit die kostenlose Version und können die Umgebung nicht löschen. Bitte kaufen Sie die professionelle Version auf der offiziellen Website.", "$files": ["src/pages/Setting/pages/DeployEnv/components/DeployEnvCard.tsx(833-878)"], "$id": 1214}, "搜索比较...": {"en-US": "Search comparison...", "de-DE": "Suchvergleich...", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/ResultContent.tsx(2170-2181)"], "$id": 1197}, "分类": {"en-US": "Classification", "de-DE": "Klassifizierung", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/ResultContent.tsx(2850-2856)"], "$id": 1198}, "长度": {"en-US": "Length", "de-DE": "<PERSON><PERSON><PERSON>", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/ResultContent.tsx(3131-3137)"], "$id": 1199}, "所有长度": {"en-US": "All lengths", "de-DE": "Alle Längen", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/ResultContent.tsx(3249-3257)"], "$id": 1200}, "短文本": {"en-US": "Short text", "de-DE": "Kurzer Text", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/ResultContent.tsx(3316-3323)", "src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/components/CompareList.tsx(2191-2198)"], "$id": 1201}, "中等文本": {"en-US": "Medium text", "de-DE": "<PERSON><PERSON><PERSON>", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/ResultContent.tsx(3383-3391)", "src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/components/CompareList.tsx(2274-2282)"], "$id": 1202}, "长文本": {"en-US": "Long text", "de-DE": "Langer Text", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/ResultContent.tsx(3449-3456)", "src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/components/CompareList.tsx(2304-2311)"], "$id": 1203}, "排序": {"en-US": "Sort", "de-DE": "<PERSON><PERSON><PERSON><PERSON>", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/ResultContent.tsx(3683-3689)"], "$id": 1204}, "最新": {"en-US": "Latest", "de-DE": "Neueste", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/ResultContent.tsx(3804-3810)"], "$id": 1205}, "最早": {"en-US": "Earliest", "de-DE": "Älteste", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/ResultContent.tsx(3870-3876)"], "$id": 1206}, "评分降序": {"en-US": "Rating descending", "de-DE": "Bewertung absteigend", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/ResultContent.tsx(3939-3947)"], "$id": 1207}, "秒前": {"en-US": "Seconds ago", "de-DE": "Sekunden zuvor", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/utils.ts(383-389)"], "$id": 1208}, "分钟前": {"en-US": "Minutes ago", "de-DE": "Minuten zuvor", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/utils.ts(503-510)"], "$id": 1209}, "小时前": {"en-US": "Hours ago", "de-DE": "Stund<PERSON> zuvor", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/utils.ts(618-625)"], "$id": 1210}, "天前": {"en-US": "Days ago", "de-DE": "<PERSON><PERSON> zuvor", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/utils.ts(728-734)"], "$id": 1211}, "个月前": {"en-US": "Months ago", "de-DE": "<PERSON><PERSON>", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/utils.ts(842-849)"], "$id": 1212}, "年前": {"en-US": "Years ago", "de-DE": "<PERSON><PERSON><PERSON> zu<PERSON>", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/utils.ts(934-940)"], "$id": 1213}, "保存模型路径": {"en-US": "Save model path", "de-DE": "Modellpfad speichern", "$files": ["src/pages/ModelSetting/pages/SavedModel/pages/log/page.tsx(594-604)"], "$id": 1189}, "部署模型名称": {"en-US": "Deploy model name", "de-DE": "Bereitstellungsmodellname", "$files": ["src/pages/ModelSetting/pages/SavedModel/pages/log/page.tsx(685-695)"], "$id": 1190}, "服务器地址": {"en-US": "Server address", "de-DE": "Server<PERSON><PERSON><PERSON>", "$files": ["src/pages/ModelSetting/pages/SavedModel/pages/log/page.tsx(1115-1124)"], "$id": 1191}, "量化": {"en-US": "Quantization", "de-DE": "Quantifizierung", "$files": ["src/pages/ModelSetting/pages/SavedModel/pages/log/page.tsx(1197-1203)"], "$id": 1192}, "部署模型日志": {"en-US": "Model Deploying Logs", "de-DE": "Protokolle der Modellausbringung", "$files": ["src/pages/ModelSetting/pages/SavedModel/pages/log/page.tsx(2298-2308)"], "$id": 311}, "保存模型名称": {"en-US": "Save Model Name", "de-DE": "<PERSON><PERSON>ame s<PERSON>ichern", "$files": ["src/pages/ModelSetting/pages/TrainedModel/pages/log/page.tsx(850-860)"], "$id": 1196}, "保存模型日志": {"en-US": "Model Saving Logs", "de-DE": "Protokolle zur Modellspeicherung", "$files": ["src/pages/ModelSetting/pages/TrainedModel/pages/log/page.tsx(2318-2328)"], "$id": 1564}, "收起": {"en-US": "Collapse", "de-DE": "Einklappen", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/components/CollapsibleText.tsx(1592-1598)"], "$id": 1216}, "展开全部": {"en-US": "Expand all", "de-DE": "Alle ausklappen", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/components/CollapsibleText.tsx(1755-1763)"], "$id": 1217}, "字符": {"en-US": "Character", "de-DE": "<PERSON><PERSON><PERSON>", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/components/CollapsibleText.tsx(1782-1788)"], "$id": 1218}, "详细信息": {"en-US": "Details", "de-DE": "Details", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/components/CompareDetail.tsx(767-775)"], "$id": 1219}, "分栏视图": {"en-US": "Split view", "de-DE": "Spaltenansicht", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/components/CompareDetail.tsx(1191-1199)"], "$id": 1220}, "选项卡视图": {"en-US": "Tab view", "de-DE": "Tabellenansicht", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/components/CompareDetail.tsx(1409-1418)"], "$id": 1221}, "输入": {"en-US": "Input", "de-DE": "Eingabe", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/components/CompareDetail.tsx(1579-1585)"], "$id": 1222}, "模型A输出": {"en-US": "Model A Output", "de-DE": "Modell A Ausgabe", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/components/CompareDetail.tsx(2671-2680)", "src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/components/CompareDetail.tsx(4532-4541)"], "$id": 1223}, "模型B输出": {"en-US": "Model B Output", "de-DE": "Modell B Ausgabe", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/components/CompareDetail.tsx(3003-3012)", "src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/components/CompareDetail.tsx(4791-4800)"], "$id": 1224}, "裁判模型评估": {"en-US": "Referee Model Evaluation", "de-DE": "Schiedsrichtermodell Bewertung", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/components/CompareDetail.tsx(3565-3575)", "src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/components/CompareDetail.tsx(5126-5136)"], "$id": 1225}, "胜出": {"en-US": "Win", "de-DE": "Gewonnen", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/components/CompareDetail.tsx(6525-6531)"], "$id": 1226}, "没有找到匹配的比较结果": {"en-US": "No matching comparison results found", "de-DE": "<PERSON>ine übereinstimmenden Vergleichsergebnisse gefunden", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/components/CompareList.tsx(569-584)"], "$id": 1227}, "回答": {"en-US": "Answer", "de-DE": "Antwort", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/components/CompareList.tsx(1069-1075)"], "$id": 1228}, "模型A胜出": {"en-US": "Model A Wins", "de-DE": "Modell A gewinnt", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/components/CompareList.tsx(1446-1455)"], "$id": 1229}, "模型B胜出": {"en-US": "Model B Wins", "de-DE": "Modell B gewinnt", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/components/CompareList.tsx(1539-1548)"], "$id": 1230}, "平局": {"en-US": "Draw", "de-DE": "Unentschieden", "$files": ["src/pages/ModelEvaluation/children/ModelEvaluationResult/components/ResultContent/components/CompareList.tsx(1572-1578)"], "$id": 1231}, "问答对": {"en-US": "Q&A Pairs", "de-DE": "Frage-Antwort-Paare", "$files": ["src/pages/Datasets/children/DatasetsDetails/components/DataPart.tsx"], "$id": 1232}, "图片1": {"en-US": "Image 1", "de-DE": "Bild 1", "$files": ["src/pages/Datasets/children/DatasetsDetails/components/DataPart.tsx"], "$id": 1233}, "图片1标注": {"en-US": "Image 1 Annotation", "de-DE": "Bild 1 Annotation", "$files": ["src/pages/Datasets/children/DatasetsDetails/components/DataPart.tsx"], "$id": 1234}, "图片2": {"en-US": "Image 2", "de-DE": "Bild 2", "$files": ["src/pages/Datasets/children/DatasetsDetails/components/DataPart.tsx"], "$id": 1235}, "图片2标注": {"en-US": "Image 2 Annotation", "de-DE": "Bild 2 Annotation", "$files": ["src/pages/Datasets/children/DatasetsDetails/components/DataPart.tsx"], "$id": 1236}, "问题": {"en-US": "Question", "de-DE": "Frage", "$files": ["src/pages/Datasets/children/DatasetsDetails/components/DataPart.tsx"], "$id": 1237}, "答案": {"en-US": "Answer", "de-DE": "Antwort", "$files": ["src/pages/Datasets/children/DatasetsDetails/components/DataPart.tsx"], "$id": 1238}, "加载中": {"en-US": "Loading", "de-DE": "Laden", "$files": ["src/pages/Datasets/children/DatasetsDetails/components/DataPart.tsx"], "$id": 1239}}